package repository

import (
	"context"
	"cx/app/models"
	"fmt"
	"time"

	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

type StudyPlanRepo interface {
	Create(ctx context.Context, memberID uint) (*models.StudyPlan, error)
	Fetch(ctx context.Context, page *models.PageOption, filter *models.StudyPlanFilter) ([]models.StudyPlan, error)
	FetchOverview(ctx context.Context, page *models.PageOption, filter *models.StudyPlanFilter) ([]models.StudyPlanSummary, error)
	GetByID(ctx context.Context, planID int64) (models.StudyPlan, error)
	GetByMemberID(ctx context.Context, memberID uint) (models.StudyPlan, error)
	DeletePlan(ctx context.Context, planID int64) error
	DeletePlans(ctx context.Context, planIDs []int64) error

	AcceptPlan(ctx context.Context, planID int64) error
	CompletePlan(ctx context.Context, planID int64) error
	RejectPlan(ctx context.Context, payload *models.StudyPlanRejectPayload) error
	RetryPlan(ctx context.Context, plan *models.StudyPlan) error

	FetchOverduePlans(ctx context.Context) ([]models.StudyPlan, error)

	FetchTasks(ctx context.Context, filter *models.StudyTaskFilter) ([]models.StudyTask, error)
	GetTaskByID(ctx context.Context, taskID int64) (models.StudyTask, error)
	CreateTask(ctx context.Context, task *models.StudyTask) error
	UpdateTask(ctx context.Context, task *models.StudyTask) error
	DeleteTask(ctx context.Context, taskID int64) error
}

type studyPlanRepo struct {
	db *gorm.DB
}

func NewStudyPlanRepo(db *gorm.DB) StudyPlanRepo {
	return &studyPlanRepo{db}
}

func (r *studyPlanRepo) Create(ctx context.Context, memberID uint) (*models.StudyPlan, error) {
	plan := models.StudyPlan{
		MemberID: memberID,
		Status:   models.PlanStatusPending,
	}

	if err := r.db.WithContext(ctx).Create(&plan).Error; err != nil {
		return nil, err
	}

	return &plan, nil
}

func (r *studyPlanRepo) Fetch(ctx context.Context, page *models.PageOption, filter *models.StudyPlanFilter) ([]models.StudyPlan, error) {
	tx := r.db.WithContext(ctx)

	if filter.MemberID > 0 {
		tx = tx.Where("member_id = ?", filter.MemberID)
	}

	if filter.IsHistory {
		tx = tx.Where("status NOT IN (?)", []models.PlanStatus{models.PlanStatusPending, models.PlanStatusActive})
	}

	var plans []models.StudyPlan

	tx = models.GetPageCounts(tx, models.StudyPlan{}, page)

	if err := tx.Preload("Tasks").Preload("Tasks.Product").
		Order("created_at DESC").
		Find(&plans).Error; err != nil {
		return nil, err
	}

	return plans, nil
}

func (r *studyPlanRepo) GetByID(ctx context.Context, planID int64) (models.StudyPlan, error) {
	var plan models.StudyPlan

	if err := r.db.WithContext(ctx).Preload("Tasks").Preload("Tasks.Product").
		First(&plan, planID).Error; err != nil {
		return plan, err
	}

	return plan, nil
}

// 取得會員當前的進修課表
func (r *studyPlanRepo) GetByMemberID(ctx context.Context, memberID uint) (models.StudyPlan, error) {
	plan := models.StudyPlan{}

	if err := r.db.WithContext(ctx).Preload("Tasks", func(db *gorm.DB) *gorm.DB {
		return db.Select("*", fmt.Sprintf("(SELECT MAX(created_at) FROM product_orders WHERE product_id = study_tasks.product_id AND member_id = %d) AS last_order_at", memberID))
	}).Preload("Tasks.Product").
		Where("member_id = ?", memberID).
		Order("created_at DESC").
		First(&plan).Error; err != nil {
		return plan, err
	}

	return plan, nil
}

func (r *studyPlanRepo) AcceptPlan(ctx context.Context, planID int64) error {
	return r.db.WithContext(ctx).Model(&models.StudyPlan{}).
		Where("id = ?", planID).
		Update("status", models.PlanStatusActive).Error
}

func (r *studyPlanRepo) CompletePlan(ctx context.Context, planID int64) error {
	return r.db.WithContext(ctx).Model(&models.StudyPlan{}).
		Where("id = ?", planID).
		Updates(map[string]interface{}{
			"status":       models.PlanStatusCompleted,
			"completed_at": null.TimeFrom(time.Now()),
		}).Error
}

func (r *studyPlanRepo) RejectPlan(ctx context.Context, payload *models.StudyPlanRejectPayload) error {
	return r.db.WithContext(ctx).Model(&models.StudyPlan{}).
		Where("id = ?", payload.PlanID).
		Updates(map[string]interface{}{
			"status":    payload.Status,
			"reason":    payload.Reason,
			"failed_at": null.TimeFrom(time.Now()),
		}).Error
}

func (r *studyPlanRepo) RetryPlan(ctx context.Context, plan *models.StudyPlan) error {
	tx := r.db.WithContext(ctx)

	err := tx.Model(&models.StudyPlan{}).Where("id = ?", plan.ID).
		Updates(map[string]interface{}{
			"status":    models.PlanStatusActive,
			"reason":    "",
			"failed_at": nil,
		}).Error
	if err != nil {
		return err
	}

	return r.createRetryLog(ctx, plan)
}

func (r *studyPlanRepo) createRetryLog(ctx context.Context, plan *models.StudyPlan) error {
	tx := r.db.WithContext(ctx)

	return tx.Create(&models.StudyPlanRetryLog{
		MemberID: plan.MemberID,
		PlanID:   plan.ID,
	}).Error
}

// 取得逾期未完成的課表任務
func (r *studyPlanRepo) FetchOverduePlans(ctx context.Context) ([]models.StudyPlan, error) {
	var plans []models.StudyPlan

	tx := r.db.WithContext(ctx)

	if err := tx.Preload("Tasks",
		"status = ? AND DATE(order_deadline) < DATE(?)",
		models.StudyTaskStatusPending, time.Now()).
		Where("status = ?", models.PlanStatusActive).
		Find(&plans).Error; err != nil {
		return nil, err
	}

	return plans, nil
}

func (r *studyPlanRepo) FetchTasks(ctx context.Context, filter *models.StudyTaskFilter) ([]models.StudyTask, error) {
	tx := r.db.WithContext(ctx)

	if filter.PlanID > 0 {
		tx = tx.Where("plan_id = ?", filter.PlanID)
	}

	if len(filter.Status) > 0 {
		tx = tx.Where("status IN ?", filter.Status)
	}

	var tasks []models.StudyTask

	if err := tx.Preload("Product").Find(&tasks).Error; err != nil {
		return nil, err
	}

	return tasks, nil
}

func (r *studyPlanRepo) GetTaskByID(ctx context.Context, taskID int64) (models.StudyTask, error) {
	var task models.StudyTask

	if err := r.db.WithContext(ctx).Preload("Product").First(&task, taskID).Error; err != nil {
		return task, err
	}

	return task, nil
}

func (r *studyPlanRepo) CreateTask(ctx context.Context, task *models.StudyTask) error {
	return r.db.WithContext(ctx).Create(task).Error
}

func (r *studyPlanRepo) UpdateTask(ctx context.Context, task *models.StudyTask) error {
	return r.db.WithContext(ctx).Model(&models.StudyTask{}).Where("id = ?", task.ID).Updates(task).Error
}

func (r *studyPlanRepo) DeleteTask(ctx context.Context, taskID int64) error {
	return r.db.WithContext(ctx).Delete(&models.StudyTask{}, taskID).Error
}

func (r *studyPlanRepo) FetchOverview(ctx context.Context, page *models.PageOption, filter *models.StudyPlanFilter) ([]models.StudyPlanSummary, error) {
	var summaries []models.StudyPlanSummary

	// 建立基本查詢，取得最新的StudyPlan和相關的Member資訊
	tx := r.db.WithContext(ctx).
		Table("study_plans sp").
		Select(`
			sp.id,
			sp.member_id,
			m.name as member_name,
			m.uid as member_uid,
			sp.status,
			sp.created_at,
			sp.updated_at,
			sp.reason,
			COUNT(st.id) as total_count,
			SUM(CASE WHEN st.status = 'pending' THEN 1 ELSE 0 END) as pending_count,
			SUM(CASE WHEN st.status = 'failed' THEN 1 ELSE 0 END) as failed_count,
			SUM(CASE WHEN st.status = 'completed' THEN 1 ELSE 0 END) as completed_count,
			MIN(CASE WHEN st.status = 'pending' THEN st.order_deadline END) as next_order_date
		`).
		Joins("INNER JOIN members m ON sp.member_id = m.id").
		Joins("LEFT JOIN study_tasks st ON sp.id = st.plan_id AND st.deleted_at IS NULL")

	// 新增搜尋條件
	if filter.MemberName != "" {
		tx = tx.Where("m.name LIKE ?", "%"+filter.MemberName+"%")
	}
	if filter.MemberUID != "" {
		tx = tx.Where("m.uid LIKE ?", "%"+filter.MemberUID+"%")
	}
	if filter.Status != "" {
		if filter.Status == "unset" {
			tx = tx.Where("sp.status = ? AND NOT EXISTS (SELECT 1 FROM study_tasks WHERE plan_id = sp.id)", models.PlanStatusActive)
		} else if filter.Status == "multi_failed" {
			tx = tx.Where("EXISTS (SELECT 1 FROM study_tasks WHERE plan_id = sp.id AND status = 'failed')")
		} else {
			tx = tx.Where("sp.status = ?", filter.Status)
		}
	}
	if filter.CreatedStartAt != "" {
		tx = tx.Where("DATE(sp.created_at) >= ?", filter.CreatedStartAt)
	}
	if filter.CreatedEndAt != "" {
		tx = tx.Where("DATE(sp.created_at) <= ?", filter.CreatedEndAt)
	}

	tx = tx.Group("sp.id, sp.member_id, m.name, m.uid, sp.status, sp.created_at, sp.updated_at, sp.reason")

	// 計算總數
	tx = models.GetPageCounts(tx, models.StudyPlan{}, page)

	// 排序
	if page.SortBy != "" {
		if page.AscSort {
			tx = tx.Order(page.SortBy + " ASC")
		} else {
			tx = tx.Order(page.SortBy + " DESC")
		}
	} else {
		tx = tx.Order("sp.created_at DESC")
	}

	// 執行查詢
	rows, err := tx.Rows()
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	for rows.Next() {
		var summary models.StudyPlanSummary
		err := rows.Scan(
			&summary.ID,
			&summary.MemberID,
			&summary.MemberName,
			&summary.MemberUID,
			&summary.Status,
			&summary.CreatedAt,
			&summary.UpdatedAt,
			&summary.Reason,
			&summary.TotalCount,
			&summary.PendingCount,
			&summary.FailedCount,
			&summary.CompletedCount,
			&summary.NextOrderDate,
		)
		if err != nil {
			return nil, err
		}

		// 計算完成進度百分比
		if summary.TotalCount > 0 {
			summary.ProgressPercent = float64(summary.CompletedCount) / float64(summary.TotalCount) * 100
		} else {
			summary.ProgressPercent = 0
		}

		summaries = append(summaries, summary)
	}

	return summaries, nil
}

func (r *studyPlanRepo) DeletePlan(ctx context.Context, planID int64) error {
	// 軟刪除StudyPlan
	return r.db.WithContext(ctx).Delete(&models.StudyPlan{}, planID).Error
}

func (r *studyPlanRepo) DeletePlans(ctx context.Context, planIDs []int64) error {
	return r.db.WithContext(ctx).Delete(&models.StudyPlan{}, planIDs).Error
}
