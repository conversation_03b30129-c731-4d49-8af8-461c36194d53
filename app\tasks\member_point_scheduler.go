package tasks

import (
	"fmt"
	"log"
	"time"

	"github.com/robfig/cron/v3"
	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"cx/app/models"
	"cx/app/utils"
	"cx/database"
)

// PointScheduler 點數檢查排程器
type PointScheduler struct {
	cron *cron.Cron
}

// NewPointScheduler 創建新的排程器
func NewPointScheduler() *PointScheduler {
	// 創建日誌管理器
	logger, _ := utils.NewLogger("point_scheduler")

	scheduler := PointScheduler{
		cron: cron.New(
			cron.WithLocation(time.Local),
			cron.WithSeconds(),
			cron.WithLogger(cron.VerbosePrintfLogger(logger.GetLogger())),
		),
	}
	return &scheduler
}

// Start 啟動排程器
func (s *PointScheduler) Start(spec string) error {
	_, err := s.cron.AddFunc(spec, processExpiredPoints)
	if err != nil {
		return fmt.Errorf("點數排程任務設置失敗: %v", err)
	}

	go processExpiredPoints()

	s.cron.Start()
	return nil
}

// Stop 停止排程器
func (s *PointScheduler) Stop() {
	if s.cron != nil {
		ctx := s.cron.Stop()
		<-ctx.Done()
		log.Println("點數檢查排程已停止")
	}
}

// processExpiredPoints 處理過期點數
func processExpiredPoints() {
	log.Println("開始執行點數過期檢查...")
	startTime := time.Now()

	db := database.ConnectDB()
	defer database.CloseDB(db)

	var points []models.Point
	// 查詢過期的點數記錄
	if err := db.Where("DATE(expired_at) < CURDATE() AND points > 0").
		Find(&points).Error; err != nil {
		log.Printf("查詢過期點數失敗: %v", err)
		return
	}

	if len(points) == 0 {
		log.Println("沒有需要處理的過期點數")
		return
	}

	// 批次處理過期點數
	for _, point := range points {
		if err := processPoint(db, point); err != nil {
			log.Printf("處理會員 %d 的點數失敗: %v", point.MemberID, err)
			continue
		}
	}

	// 記錄執行統計
	endTime := time.Now()
	duration := endTime.Sub(startTime)
	log.Printf("點數檢查完成，處理 %d 筆記錄，耗時: %v", len(points), duration)
}

// processPoint 處理單個點數記錄
func processPoint(db *gorm.DB, point models.Point) error {
	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 鎖定並清空點數
	if err := tx.Clauses(clause.Locking{Strength: "UPDATE"}).
		Model(&models.Point{}).
		Where("id = ?", point.ID).
		Updates(map[string]interface{}{
			"points":     0,
			"expired_at": null.TimeFromPtr(nil),
		}).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("清空點數失敗: %v", err)
	}

	// 新增點數歷史記錄
	if point.Points > 0 {
		pointHistory := models.PointHistory{
			MemberID:  point.MemberID,
			Points:    -point.Points,
			Reason:    fmt.Sprintf("——————系統通知：%s點數到期——————", point.ExpiredAt.Time.Format("2006/01/02")),
			CreatedAt: time.Now(),
		}

		if err := tx.Create(&pointHistory).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("創建點數歷史記錄失敗: %v", err)
		}
	}

	// 更新會員狀態為未啟用
	if err := tx.Model(&models.Member{}).
		Where("id = ?", point.MemberID).
		Update("status", "W").Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("更新會員狀態失敗: %v", err)
	}

	return tx.Commit().Error
}
