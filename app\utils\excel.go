package utils

import (
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
)

type ExcelOpt struct {
	SheetName string `default:"Sheet1"`
	Header    []string
	ColWidth  []float64
}

func ExportExcel(c *gin.Context, data interface{}, opt ExcelOpt) {
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Printf("Error to close file: %+v\n", err)
		}
	}()

	err := f.SetSheetName("Sheet1", opt.SheetName)
	if err != nil {
		fmt.Printf("Error to create new sheet: %+v\n", err)
		c.Data(http.StatusInternalServerError, "application/octet-stream", []byte(err.Error()))
		return
	}

	// get style
	styleID, _ := f.<PERSON>ty<PERSON>(&excelize.Style{
		Font: &excelize.Font{
			Size:   12,
			Family: "新細明體",
		},
	})

	// set column width
	for j, w := range opt.ColWidth {
		f.SetColWidth(opt.SheetName, toAlpha(j), toAlpha(j), w)
	}
	f.SetColStyle(opt.SheetName, "A:Z", styleID)

	f.SetSheetRow(opt.SheetName, "A1", &opt.Header)

	for i, v := range ToInterfaceArray(data) {
		_, values := FlattenStruct(v)
		f.SetSheetRow(opt.SheetName, fmt.Sprintf("A%d", i+2), &values)
	}

	// 將Excel檔案緩存在記憶體中
	excelBytes, _ := f.WriteToBuffer()

	// 將Excel檔案的內容轉換為byte數組
	fileBytes, _ := io.ReadAll(excelBytes)

	if err != nil {
		c.Data(http.StatusInternalServerError, "application/octet-stream", []byte(err.Error()))
		return
	}

	c.Data(http.StatusOK, "application/octet-stream", fileBytes)
}

func toAlpha(i int) string {
	return string('A' + i)
}

func ParseStrDate(date string, format string) string {
	if date == "" {
		return ""
	}

	newDate, err := time.Parse("2006-01-02 15:04:05", date)
	if err != nil {
		// fmt.Printf("Error to parse time: %+v\n", err)
		return ""
	}

	if newDate.IsZero() {
		return ""
	}

	return newDate.Format(format)
}

func MatchImportHeader(header []string, headerMap map[string]string) map[string]int {
	h := map[string]int{}

	for i, v := range header {
		if name, ok := headerMap[v]; ok {
			h[name] = i
		}
	}

	for _, v := range headerMap {
		if _, ok := h[v]; !ok {
			h[v] = -1
		}
	}

	return h
}

func ReadExcel(file *multipart.FileHeader) ([][]string, error) {
	src, err := file.Open()
	if err != nil {
		return nil, err
	}
	defer src.Close()

	f, err := excelize.OpenReader(src)
	if err != nil {
		return nil, err
	}

	sheetName := f.GetSheetName(0)
	rows, err := f.GetRows(sheetName)
	if err != nil {
		return nil, err
	}

	data := make([][]string, len(rows))
	for i, row := range rows {
		data[i] = make([]string, len(row))
		for j, cell := range row {
			data[i][j] = cell
		}
	}

	return data, nil
}

// 倒著匯入資料
func ImportData(conn *gorm.DB, list []map[string]interface{}, model interface{}) (string, error) {
	tableName := GetTableName(conn, model)
	batchSize := 10
	for i := 0; i < len(list); i += batchSize {
		var createData []map[string]interface{}
		if i+batchSize >= len(list) {
			createData = list[i:]
		} else {
			createData = list[i : i+batchSize]
		}

		if err := conn.Table(tableName).Create(createData).Error; err != nil {
			return "匯入時發生錯誤", err
		}
	}

	return "", nil
}

func GetValueByIndex(val []string, index int) string {
	if index >= len(val) || index < 0 {
		return ""
	}

	return val[index]
}

// CSV 匯出選項
type CSVOpt struct {
	FileName string
	Header   []string
}

// 匯出 CSV 格式
func ExportCSV(c *gin.Context, data interface{}, opt CSVOpt) {
	var csvContent strings.Builder

	// 寫入 BOM 以支援中文
	csvContent.WriteString("\xEF\xBB\xBF")

	// 寫入標題行
	for i, header := range opt.Header {
		if i > 0 {
			csvContent.WriteString(",")
		}
		csvContent.WriteString(fmt.Sprintf("\"%s\"", strings.ReplaceAll(header, "\"", "\"\"")))
	}
	csvContent.WriteString("\n")

	// 寫入資料行
	for _, v := range ToInterfaceArray(data) {
		_, values := FlattenStruct(v)
		for i, value := range values {
			if i > 0 {
				csvContent.WriteString(",")
			}
			// 處理值，確保 CSV 格式正確
			strValue := fmt.Sprintf("%v", value)
			// 如果包含逗號、引號或換行符，需要用引號包圍
			if strings.Contains(strValue, ",") || strings.Contains(strValue, "\"") || strings.Contains(strValue, "\n") {
				strValue = fmt.Sprintf("\"%s\"", strings.ReplaceAll(strValue, "\"", "\"\""))
			}
			csvContent.WriteString(strValue)
		}
		csvContent.WriteString("\n")
	}

	// 設定檔案名稱
	fileName := opt.FileName
	if fileName == "" {
		fileName = "export.csv"
	}
	if !strings.HasSuffix(fileName, ".csv") {
		fileName += ".csv"
	}

	// 設定回應標頭
	c.Header("Content-Type", "text/csv; charset=utf-8")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", fileName))

	// 回傳 CSV 內容
	c.String(http.StatusOK, csvContent.String())
}
