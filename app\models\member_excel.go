package models

import (
	"encoding/json"
	"strings"

	. "cx/app/utils"
)

var MemberImportMap = map[string]string{
	"會員帳號":         "uid",
	"姓名":           "name",
	"暱稱":           "nick_name",
	"性別":           "sex",
	"連絡電話":         "phone",
	"Line ID":      "line_id",
	"Instagram ID": "ig_id",
	"生日":           "birthday",
	"地址":           "address",
	"適用等級":         "level",
	"到期日":          "expired_at",
	"狀態":           "status",
}

type ExcelImport interface {
	HandleImportData([]string, []map[string]int)
}

type MemberImport struct {
	Uid       string `json:"uid"`
	Pwd       string `json:"pwd"`
	Name      string `json:"name"`
	NickName  string `json:"nick_name"`
	Sex       string `json:"sex"`
	Phone     string `json:"phone"`
	LineID    string `json:"line_id"`
	IgID      string `json:"ig_id"`
	BirthdayY string `json:"birthday_y"`
	BirthdayM string `json:"birthday_m"`
	BirthdayD string `json:"birthday_d"`
	Country   string `json:"country"`
	Zipcode   string `json:"zipcode"`
	Address   string `json:"address"`
	Level     int    `json:"level"`
	Status    string `json:"status"`
}

func (m *MemberImport) MarshalJSON() ([]byte, error) {
	return json.Marshal(m)
}

func (m *MemberImport) UnmarshalJSON(data []byte) error {
	return json.Unmarshal(data, m)
}

func (m *MemberImport) HandleImportData(v []string, h map[string]int) {
	var (
		pwd                                string
		birthday_y, birthday_m, birthday_d string
		country, zipcode, address          string
		level                              int
		status                             string
	)

	if GetValueByIndex(v, h["uid"]) == "" {
		return
	}

	phone := handlePhoneFormat(GetValueByIndex(v, h["phone"]))

	if GetValueByIndex(v, h["birthday"]) != "" {
		birthday := strings.Split(v[h["birthday"]], "/")
		birthday_y = birthday[0]
		birthday_m = birthday[1]
		birthday_d = birthday[2]
	}

	// 密碼預設為生日
	pwd, _ = PasswordHash(birthday_y + birthday_m + birthday_d)

	if GetValueByIndex(v, h["address"]) != "" {
		if strings.Index(v[h["address"]], "中華民國") == 0 {
			country = "中華民國"
			zipcode = v[h["address"]][12:15]
			address = v[h["address"]][15:]
		} else {
			address = v[h["address"]]
		}
	}

	if GetValueByIndex(v, h["level"]) != "" {
		level = MemberLevelText[v[h["level"]]]
	} else {
		level = MemberLevel1
	}

	if GetValueByIndex(v, h["status"]) != "" {
		status = MemberStatusText[v[h["status"]]]
	} else {
		status = "N"
	}

	m.Uid = GetValueByIndex(v, h["uid"])
	m.Pwd = pwd
	m.Name = GetValueByIndex(v, h["name"])
	m.NickName = GetValueByIndex(v, h["nick_name"])
	m.Sex = GetValueByIndex(v, h["sex"])
	m.Phone = phone
	m.LineID = GetValueByIndex(v, h["line_id"])
	m.IgID = GetValueByIndex(v, h["ig_id"])
	m.BirthdayY = birthday_y
	m.BirthdayM = birthday_m
	m.BirthdayD = birthday_d
	m.Country = country
	m.Zipcode = zipcode
	m.Address = address
	m.Level = level
	m.Status = status
}

func handlePhoneFormat(phone string) string {
	newPhone := phone

	newPhone = strings.TrimSpace(newPhone)
	newPhone = strings.Trim(newPhone, "-")

	if phone == "" {
		return ""
	}

	if len(newPhone) == 9 && strings.HasPrefix(newPhone, "9") {
		newPhone = "0" + newPhone
	}

	return newPhone
}

type MemberExport struct {
	Uid            string `json:"會員帳號" col:"20"`
	Name           string `json:"姓名" col:"10"`
	NickName       string `json:"暱稱" col:"10"`
	Level          string `json:"適用等級" col:"10"`
	ExpiredAt      string `json:"等級到期日" col:"15"`
	Sex            string `json:"性別" col:"10"`
	Phone          string `json:"連絡電話" col:"15"`
	LineID         string `json:"Line ID" col:"15"`
	IgID           string `json:"Instagram ID" col:"15"`
	Birthday       string `json:"生日" col:"15"`
	Address        string `json:"地址" col:"40"`
	Points         int    `json:"剩餘點數" col:"15"`
	PointExpiredAt string `json:"點數到期日" col:"20"`
	TotalPoints    int    `json:"總儲值點數" col:"15"`
	CreatedAt      string `json:"註冊時間" col:"20"`
	LastLogin      string `json:"最後登入" col:"20"`
	Status         string `json:"狀態" col:"10"`
}

func (MemberExport) TableName() string {
	return "members"
}

func (mem *MemberExport) HandleData() {
	level := StrToInt(mem.Level)
	mem.Level = MemberLevel[level]

	mem.Status = MemberStatus[mem.Status]

	mem.ExpiredAt = ParseStrDate(mem.ExpiredAt, "2006/01/02")
	mem.PointExpiredAt = ParseStrDate(mem.PointExpiredAt, "2006/01/02")
	mem.CreatedAt = ParseStrDate(mem.CreatedAt, "2006/01/02 15:04:05")
	mem.LastLogin = ParseStrDate(mem.LastLogin, "2006/01/02 15:04:05")
}

// 會員總覽匯出結構
type MemberOverviewExport struct {
	MemberUID        string `json:"會員帳號" col:"15"`
	MemberName       string `json:"會員姓名" col:"15"`
	CategoryName     string `json:"課程類別" col:"20"`
	PurchaseCount    int    `json:"購買次數" col:"12"`
	PurchasedCourses int    `json:"已購買課程數" col:"15"`
	TotalCourses     int    `json:"總課程數" col:"12"`
	CompletionRate   string `json:"完成率" col:"10"`
	LatestPurchase   string `json:"最近購買時間" col:"20"`
}

func (MemberOverviewExport) TableName() string {
	return "member_overview_export"
}

func (export *MemberOverviewExport) HandleData() {
	export.LatestPurchase = ParseStrDate(export.LatestPurchase, "2006/01/02 15:04:05")
}

// 會員詳細課程匯出結構
type MemberDetailExport struct {
	MemberUID       string `json:"會員帳號" col:"15"`
	MemberName      string `json:"會員姓名" col:"15"`
	CategoryName    string `json:"主類別" col:"20"`
	SubCategoryName string `json:"子類別" col:"20"`
	CourseName      string `json:"課程名稱" col:"30"`
	IsPurchased     string `json:"購買狀態" col:"12"`
	PurchaseCount   int    `json:"購買次數" col:"12"`
	PurchaseDate    string `json:"購買時間" col:"20"`
}

func (MemberDetailExport) TableName() string {
	return "member_detail_export"
}

func (export *MemberDetailExport) HandleData() {
	export.PurchaseDate = ParseStrDate(export.PurchaseDate, "2006/01/02 15:04:05")
}
