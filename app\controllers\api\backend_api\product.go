package backend_api

import (
	"fmt"
	"io"
	"math"
	"net/http"
	"os"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"
	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"

	. "cx/app/models"
	"cx/app/utils"
	. "cx/app/utils"
	. "cx/database"
)

// Product
func GetProduct(c *gin.Context) {
	product := Product{}

	conn := ConnectDB()
	defer CloseDB(conn)

	if err := conn.Preload("ProOpenLevels").Preload("ProPointLevels").Preload("ProGoodsLimits").Preload("Works").
		Preload("RelatedPros", func(db *gorm.DB) *gorm.DB {
			return db.Select("pro_id", "pro_name", "related_pro_id").
				Joins("JOIN products ON products.id = product_related_pros.related_pro_id AND products.deleted_at IS NULL")
		}).
		Preload("LimitedPros", func(db *gorm.DB) *gorm.DB {
			return db.Select("pro_id", "pro_name", "limited_pro_id").
				Joins("JOIN products ON products.id = product_limited_pros.limited_pro_id AND products.deleted_at IS NULL")
		}).
		First(&product, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "查無資料",
			"error": err.Error(),
		})
		return
	}

	if err := conn.Model(&Admin{}).Unscoped().Select("admin_name").Find(&product.CreatedBy, product.CreatedByID).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "查詢失敗",
			"error": err.Error(),
		})
		return
	}

	if err := conn.Model(&Admin{}).Unscoped().Select("admin_name").Find(&product.UpdatedBy, product.UpdatedByID).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "查詢失敗",
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": product,
	})
}

func GetProducts(c *gin.Context) {
	products := []struct {
		Product        `gorm:"embedded"`
		DisplayOrderID null.Int `json:"display_order_id"`
		KindName       string   `json:"kind_name"`
	}{}
	page := PageOption{}

	c.ShouldBind(&page)

	conn := ConnectDB()
	defer CloseDB(conn)

	conn = SearchPro(c, conn, &page)

	GetPageCounts(conn, Product{}, &page)

	if err := conn.Preload("ProPointLevels").Model(&Product{}).
		Select("products.id", "pro_name", "products.pro_kind_id", "kind_name", "pro_start_at", "pro_end_at",
			"pro_point_type", "pro_points", "products.status",
			"pdo.id AS display_order_id", "IFNULL(pdo.display_order, products.sorting) AS sorting",
			"products.created_at").
		Find(&products).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"msg":   "無法取得課程列表",
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":       products,
		"pageOption": page,
	})
}

func SearchPro(c *gin.Context, conn *gorm.DB, opt *PageOption) *gorm.DB {
	search := c.QueryMap("search")

	conn = conn.Joins("LEFT JOIN product_kinds ON product_kinds.id = products.pro_kind_id").
		Joins("LEFT JOIN product_display_orders AS pdo ON pdo.product_id = products.id AND pdo.pro_kind_id = ?", search["pro_kind_id"])

	if search["pro_kind_id"] != "" && search["pro_kind_id"] != "0" {
		if search["include_child_kind"] == "Y" {
			// conn = conn.Where("products.pro_kind_id IN (SELECT DISTINCT `id` FROM `product_kinds` WHERE `id` = ? OR `parent_id` = ?)", search["pro_kind_id"], search["pro_kind_id"])
			conn = conn.Where(fmt.Sprintf(`
				products.pro_kind_id = %s OR product_kinds.id IN (%s)
			`, search["pro_kind_id"], GetKindChildrenByKindIDQuery(search["pro_kind_id"])))
		} else {
			conn = conn.Where("products.pro_kind_id = ?", search["pro_kind_id"])
		}
	}

	if search["pro_name"] != "" {
		conn = conn.Where("pro_name LIKE ?", "%"+search["pro_name"]+"%")
	}

	if search["start_at"] != "" {
		conn = conn.Where("(pro_start_at IS NULL OR DATE_FORMAT(pro_start_at, '%Y-%m-%d') <= ?)", search["start_at"])
	}

	if search["end_at"] != "" {
		conn = conn.Where("(pro_end_at IS NULL OR DATE_FORMAT(pro_end_at, '%Y-%m-%d') > ?)", search["end_at"])
	}

	if search["is_open"] == "Y" {
		conn = conn.Where("(pro_start_at IS NULL OR pro_start_at <= NOW()) AND (pro_end_at IS NULL OR pro_end_at > NOW())").
			Where("products.status = 'Y'").
			Where("product_kinds.status = 'Y'").
			Where("product_kinds.deleted_at IS NULL")
	}

	if search["status"] != "" {
		conn = conn.Where("products.status = ?", search["status"])
	}

	if opt.SortBy != "" {
		conn = conn.Order(opt.SortBy + " " + GetSortType(opt.AscSort))
	} else {
		conn = conn.Order("products.sorting ASC")
	}

	return conn
}

func CreateProduct(c *gin.Context) {
	product := &Product{}

	if err := c.ShouldBind(&product); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "錯誤資料",
			"error": err.Error(),
		})
		return
	}

	info := GetAdminInfo(c)

	product.CreatedByID = info.ID
	product.UpdatedByID = info.ID

	conn := ConnectDB()
	defer CloseDB(conn)

	conn = conn.Begin()

	if err := conn.Create(&product).Error; err != nil {
		conn.Rollback()
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "新增失敗",
			"error": err.Error(),
		})
		return
	}

	if err := product.UpdateAssociations(conn); err != nil {
		conn.Rollback()
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "新增失敗",
			"error": err.Error(),
		})
		return
	}

	conn.Commit()

	c.JSON(http.StatusOK, gin.H{
		"id":  product.ID,
		"msg": "新增成功",
	})
}

func CreateProductManual(c *gin.Context) {
	data := &struct {
		MemID    uint   `form:"mem_id" json:"mem_id"`
		ProID    uint   `form:"pro_id" json:"pro_id"`
		Points   int    `form:"points" json:"points"`
		Reason   string `form:"reason" json:"reason"`
		IsDays   string `form:"is_days" json:"is_days"`     // Y: 手動輸入天數, N: 依課程天數
		OpenDays int    `form:"open_days" json:"open_days"` // 手動輸入天數
	}{}
	pro := &Product{}
	mem := &Member{}
	memPoint := &Point{}

	if err := c.ShouldBind(&data); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "錯誤資料",
			"error": err.Error(),
		})
		return
	}

	conn := ConnectDB()
	defer CloseDB(conn)

	info := GetAdminInfo(c)

	if err := conn.First(&pro, data.ProID).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "查無課程資料",
			"error": err.Error(),
		})
		return
	}

	if err := conn.First(&mem, data.MemID).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "查無會員資料",
			"error": err.Error(),
		})
		return
	}

	if err := conn.Find(&memPoint, "member_id = ?", mem.ID).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "查無會員點數資料",
			"error": err.Error(),
		})
		return
	}

	// 確認是否已購買課程
	memPro := MemberProduct{}
	if err := conn.Select("id", "status").
		Where("status = 'S' OR expired_at > NOW()").
		Where("member_id = ? AND product_id = ?", mem.ID, pro.ID).
		Find(&memPro).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg": "無法取得會員課程",
		})
		return
	} else if memPro.ID != 0 {
		if memPro.Status == "N" {
			c.JSON(http.StatusBadRequest, gin.H{
				"msg": "此課程被停用中",
			})
			return
		} else {
			c.JSON(http.StatusBadRequest, gin.H{
				"msg": "已購買此課程",
			})
			return
		}
	}

	if err := conn.Transaction(func(tx *gorm.DB) error {
		// 新增課程購買紀錄
		proOrder := ProductOrder{
			ProductID: data.ProID,
			MemberID:  data.MemID,
			Points:    int(math.Abs(float64(data.Points))),
		}

		if err := tx.Create(&proOrder).Error; err != nil {
			return err
		}

		// 取得課程天數
		proOpenDays := 7

		if data.IsDays == "Y" {
			// 手動輸入天數
			proOpenDays = data.OpenDays
		} else {
			// 依課程設定給天數
			if pro.ProOpenType == "L" {
				// 依等級給天數
				if err := tx.Select("open_days").
					Where("pro_id = ? AND level = ?", pro.ID, mem.Level).
					Scan(&proOpenDays).Error; err != nil {
					return err
				}
			} else {
				proOpenDays = pro.ProStatusOpen
			}
		}

		// 新增會員課程
		if err := tx.Create(&MemberProduct{
			MemberID:        mem.ID,
			ProductID:       pro.ID,
			AllowCounseling: "Y",
			IsReview:        "N",
			IsRelive:        "N",
			Status:          "Y",
			ExpiredAt:       time.Now().Add(time.Duration(proOpenDays) * 24 * time.Hour),
		}).Error; err != nil {
			return err
		}

		// point
		if memPoint.ID == 0 {
			if err := tx.Create(&Point{
				MemberID: mem.ID,
				Points:   data.Points,
			}).Error; err != nil {
				return err
			}
		} else {
			if err := tx.Model(&memPoint).Update("points", gorm.Expr("points - ?", data.Points)).Error; err != nil {
				return err
			}
		}

		// point history
		if data.Reason == "" {
			data.Reason = "手動發送課程 - " + pro.ProName
		}

		if err := tx.Create(&PointHistory{
			MemberID:       mem.ID,
			ProductOrderID: null.IntFrom((int64)(proOrder.ID)),
			Points:         data.Points,
			Reason:         data.Reason,
			CreatedByID:    null.IntFrom((int64)(info.ID)),
		}).Error; err != nil {
			return err
		}

		return nil
	}); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "發送失敗",
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"msg": "發送成功",
	})
}

func UpdateProduct(c *gin.Context) {
	product := &Product{}

	if err := c.ShouldBind(&product); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "資料錯誤",
			"error": err.Error(),
		})
		return
	}

	info := GetAdminInfo(c)
	product.UpdatedByID = info.ID

	conn := ConnectDB()
	defer CloseDB(conn)

	conn = conn.Begin()

	omit := []string{"sorting"}

	if err := conn.Omit(omit...).Save(&product).Error; err != nil {
		conn.Rollback()
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "更新失敗",
			"error": err.Error(),
		})
		return
	}

	if err := product.UpdateAssociations(conn); err != nil {
		conn.Rollback()
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "更新失敗",
			"error": err.Error(),
		})
		return
	}

	conn.Commit()

	c.JSON(http.StatusOK, gin.H{
		"msg": "更新成功",
	})
}

func UpdateProductStatus(c *gin.Context) {
	status := c.PostForm("status")

	if status != "Y" && status != "N" {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg": "狀態錯誤，無法更新",
		})
		return
	}

	conn := ConnectDB()
	defer CloseDB(conn)

	info := GetAdminInfo(c)

	if err := conn.Model(&Product{}).Where("id = ?", c.Param("id")).
		Updates(map[string]interface{}{
			"status":        status,
			"updated_by_id": info.ID,
		}).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "更新失敗",
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"msg": "更新成功",
	})
}

func UpdateProductSorting(c *gin.Context) {
	req := []ProductDisplayOrder{}

	if err := utils.BindArray(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "更新失敗",
			"error": err.Error(),
		})
		return
	}

	conn := ConnectDB()
	defer CloseDB(conn)

	conn = conn.Begin()

	for _, r := range req {
		if r.ID > 0 {
			if err := conn.Model(&ProductDisplayOrder{}).Where("id = ?", r.ID).
				Update("display_order", r.DisplayOrder).Error; err != nil {
				conn.Rollback()
				c.JSON(http.StatusBadRequest, gin.H{
					"msg":   "更新失敗",
					"error": err.Error(),
				})
				return
			}
		} else {
			if err := conn.Create(&r).Error; err != nil {
				conn.Rollback()
				c.JSON(http.StatusBadRequest, gin.H{
					"msg":   "更新失敗",
					"error": err.Error(),
				})
				return
			}
		}
	}

	conn.Commit()

	c.JSON(http.StatusOK, gin.H{
		"msg": "更新成功",
	})
}

func UploadProductImg(c *gin.Context) {
	product := &Product{}
	img, _ := c.FormFile("upload_img")

	conn := ConnectDB()
	defer CloseDB(conn)

	if err := conn.Select("id", "pro_img").First(&product, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "查無資料",
			"error": err.Error(),
		})
		return
	}

	del_img, _ := strconv.ParseBool(c.PostForm("del_img"))

	if del_img {
		if err := os.Remove("assets" + product.ProImg); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"msg":   "刪除圖片失敗",
				"error": err.Error(),
			})
			return
		}

		product.ProImg = ""
	}

	if img != nil {
		now := time.Now().Format("20060102150405")
		basePath := "./uploads/images/product/"
		product.ProImg = basePath + now + "_" + strconv.FormatUint(uint64(product.ID), 10) + "_" + img.Filename

		if err := Mkdir(basePath); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"msg":   "建立資料夾失敗",
				"error": err.Error(),
			})
			return
		}

		if err := c.SaveUploadedFile(img, "assets"+product.ProImg); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"msg":   "圖片上傳失敗",
				"error": err.Error(),
			})
			return
		}
	}

	info := GetAdminInfo(c)
	product.UpdatedByID = info.ID

	if err := conn.Select("pro_img", "updated_by_id").Save(&product).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "圖片更新失敗",
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"pro_img": product.ProImg,
	})
}

func DeleteProducts(c *gin.Context) {
	ids := &struct {
		IDs []uint `json:"ids"`
	}{}

	if err := c.ShouldBind(&ids); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "無法取得要刪除的資料",
			"error": err.Error(),
		})
		return
	}

	if len(ids.IDs) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg": "無法取得要刪除的資料",
		})
		return
	}

	conn := ConnectDB()
	defer CloseDB(conn)

	conn = conn.Begin()

	if err := conn.Where("id IN ?", ids.IDs).Delete(&Product{}).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "刪除失敗",
			"error": err.Error(),
		})
		return
	}

	conn.Commit()

	c.JSON(http.StatusOK, gin.H{
		"msg": "刪除成功",
	})

}

func DeleteProduct(c *gin.Context) {
	conn := ConnectDB()
	defer CloseDB(conn)

	if err := conn.Delete(&Product{}, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "刪除失敗",
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"msg": "刪除成功",
	})
}

func GetMemberProducts(c *gin.Context) {
	memPros := []struct {
		ProID     uint   `json:"pro_id"`
		ProName   string `json:"pro_name"`
		MemProID  uint   `json:"mem_pro_id"`
		ExpiredAt string `json:"expired_at"`
	}{}

	search := c.QueryMap("search")

	conn := ConnectDB()
	defer CloseDB(conn)

	now := time.Now()

	if search["status"] != "" {
		conn = conn.Where("member_products.status = ?", search["status"])
	}

	if search["expired"] == "Y" {
		conn = conn.Where("member_products.expired_at IS NOT NULL AND member_products.expired_at <= ?", now)
	} else if search["expired"] == "N" {
		conn = conn.Where("member_products.expired_at IS NULL OR member_products.expired_at > ?", now)
	}

	// 取得上過課的課程，不重複課程編號
	if search["taken"] == "Y" {
		conn = conn.Group("products.id")
	}

	// get member_products distinct pro_id
	if err := conn.Model(&MemberProduct{}).Select("products.id AS pro_id", "pro_name", "member_products.id AS mem_pro_id", "member_products.expired_at").
		Joins("LEFT JOIN products ON products.id = member_products.product_id").
		Where("member_products.member_id = ?", c.Param("member_id")).
		Find(&memPros).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"msg":   "無法取得作業",
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": memPros,
	})
}

func GetProductSearch(c *gin.Context) {
	proKinds := []struct {
		ID       uint   `json:"id"`
		ParentID uint   `json:"parent_id"`
		Name     string `gorm:"column:kind_name" json:"name"`
	}{}
	pros := []struct {
		ID     uint   `json:"id"`
		KindID uint   `gorm:"column:pro_kind_id" json:"kind_id"`
		Name   string `gorm:"column:pro_name" json:"name"`
	}{}

	conn := ConnectDB()
	defer CloseDB(conn)

	if err := conn.Model(&ProductKind{}).Select("id", "parent_id", "kind_name").
		Order("parent_id ASC, sorting ASC").
		Find(&proKinds).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"msg":   "無法取得課程分類",
			"error": err.Error(),
		})
		return
	}

	if err := conn.Model(&Product{}).Select("id", "products.pro_kind_id", "pro_name").
		Order("sorting ASC").
		Find(&pros).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"msg":   "無法取得課程",
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"pro_kinds": proKinds,
		"pros":      pros,
	})
}

func UpdateMemberProductStatus(c *gin.Context) {
	mem_pro := &MemberProduct{}

	status := c.PostForm("status")

	conn := ConnectDB()
	defer CloseDB(conn)

	conn = conn.Begin()

	if status != "Y" && status != "N" && status != "S" {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg": "狀態錯誤，無法更新",
		})
		return
	}

	if err := conn.First(&mem_pro, c.Param("mem_pro_id")).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "查無資料",
			"error": err.Error(),
		})
		return
	}

	if mem_pro.Status == "S" && status != "S" {
		// 課程狀態為暫停中，更新為其他狀態
		if err := mem_pro.ResumePro(conn, status); err != nil {
			conn.Rollback()
			c.JSON(http.StatusBadRequest, gin.H{
				"msg":   "更新失敗",
				"error": err.Error(),
			})
			return
		}
	} else if mem_pro.Status != "S" && status == "S" {
		// 暫停課程
		if err := mem_pro.StopPro(conn, 30); err != nil {
			conn.Rollback()
			c.JSON(http.StatusBadRequest, gin.H{
				"msg":   "更新失敗",
				"error": err.Error(),
			})
			return
		}
	}

	// 更新課程狀態
	if err := conn.Model(&mem_pro).
		Update("status", status).Error; err != nil {
		conn.Rollback()
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "更新失敗",
			"error": err.Error(),
		})
		return
	}

	conn.Commit()

	c.JSON(http.StatusOK, gin.H{
		"msg": "更新成功",
	})
}

func UpdateMemberProduct(c *gin.Context) {
	data := &MemberProduct{}
	fields := c.QueryArray("fields")

	if err := c.ShouldBindJSON(&data); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "資料錯誤",
			"error": err.Error(),
		})
		return
	}

	conn := ConnectDB()
	defer CloseDB(conn)

	conn = conn.Begin()

	if utils.InArray("status", fields) > -1 {
		memPro := &MemberProduct{}

		if err := conn.First(&memPro, c.Param("mem_pro_id")).Error; err != nil {
			conn.Rollback()
			c.JSON(http.StatusBadRequest, gin.H{
				"msg":   "查無資料",
				"error": err.Error(),
			})
			return
		}

		if err := memPro.HandleStatusUpdate(conn, data.Status); err != nil {
			conn.Rollback()
			c.JSON(http.StatusBadRequest, gin.H{
				"msg":   "更新失敗",
				"error": err.Error(),
			})
			return
		}
	}

	index := utils.InArray("expired_at", fields)
	if index > -1 {
		fields = append(fields[:index], fields[index+1:]...)

		if !data.ExpiredAt.IsZero() {
			if err := conn.Model(&MemberProduct{}).Where("id = ?", c.Param("mem_pro_id")).
				Update("expired_at", gorm.Expr("CONCAT(DATE_FORMAT(?, '%Y-%m-%d '), DATE_FORMAT(expired_at, '%H:%i:%s'))", data.ExpiredAt)).
				Error; err != nil {
				conn.Rollback()
				c.JSON(http.StatusBadRequest, gin.H{
					"msg":   "更新失敗",
					"error": err.Error(),
				})
				return
			}
		}
	}

	if len(fields) > 0 {
		if err := conn.Select(fields).Model(&MemberProduct{}).Where("id = ?", c.Param("mem_pro_id")).
			Updates(&data).Error; err != nil {
			conn.Rollback()
			c.JSON(http.StatusBadRequest, gin.H{
				"msg":   "更新失敗",
				"error": err.Error(),
			})
			return
		}
	}

	conn.Commit()

	c.JSON(http.StatusOK, gin.H{
		"msg": "更新成功",
	})
}

func GetMemberOverview(c *gin.Context) {
	overviews := []MemProOverview{}

	conn := ConnectDB()
	defer CloseDB(conn)

	if err := conn.Model(&Product{}).
		Select("products.id AS pro_id", "pro_name", "kinds.id AS kind_id",
			"IF(pro_end_at <= CURRENT_TIMESTAMP(), 'N', products.status) AS pro_status",
			"COUNT(mem_pro.created_at) AS buy_times", "MAX(mem_pro.created_at) AS buy_at").
		Joins(`
			LEFT JOIN member_products AS mem_pro ON mem_pro.product_id = products.id 
				AND mem_pro.member_id = ? 
				AND mem_pro.is_review = 'N'
				AND mem_pro.deleted_at IS NULL
		`, c.Param("member_id")).
		Joins("LEFT JOIN product_kinds AS kinds ON kinds.id = products.pro_kind_id").
		Group("products.id").
		Order("products.sorting ASC").
		Find(&overviews).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"msg":   "無法取得資料",
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": overviews,
	})
}

func ExportMemberOverview(c *gin.Context) {
	memberID := c.Param("member_id")
	category := c.Query("category")
	sortBy := c.Query("sort_by")
	sortOrder := c.Query("sort_order")
	exportType := c.Query("export_type")
	detailFilter := c.Query("detail_filter")
	includeOffline := c.Query("include_offline") == "true"

	// 取得會員資訊
	member := Member{}
	conn := ConnectDB()
	defer CloseDB(conn)

	if err := conn.Select("uid", "name").Find(&member, memberID).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"msg":   "無法取得會員資料",
			"error": err.Error(),
		})
		return
	}

	// 取得產品類別關係
	proKinds := []ProKindRelation{}
	if err := conn.Raw(`
		WITH RECURSIVE kind_hierarchy AS (
			SELECT id AS kind_id, id AS root_id, parent_id, kind_name, sorting, 0 AS level
			FROM product_kinds
			WHERE parent_id = 0 AND deleted_at IS NULL

			UNION ALL

			SELECT pk.id AS kind_id, kh.root_id, pk.parent_id, pk.kind_name, pk.sorting, kh.level + 1
			FROM product_kinds pk
			INNER JOIN kind_hierarchy kh ON pk.parent_id = kh.kind_id
			WHERE pk.deleted_at IS NULL
		)
		SELECT kind_id, root_id, parent_id, kind_name, sorting FROM kind_hierarchy
		ORDER BY root_id, level, sorting
	`).Scan(&proKinds).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"msg":   "無法取得產品類別資料",
			"error": err.Error(),
		})
		return
	}

	// 取得會員總覽資料
	overviews := []MemProOverview{}
	query := conn.Model(&Product{}).
		Select("products.id AS pro_id", "pro_name", "kinds.id AS kind_id",
			"IF(pro_end_at <= CURRENT_TIMESTAMP(), 'N', products.status) AS pro_status",
			"COUNT(mem_pro.created_at) AS buy_times",
			"DATE_FORMAT(MAX(mem_pro.created_at), '%Y-%m-%d %H:%i:%s') AS buy_at").
		Joins(`
			LEFT JOIN member_products AS mem_pro ON mem_pro.product_id = products.id
				AND mem_pro.member_id = ?
				AND mem_pro.is_review = 'N'
				AND mem_pro.deleted_at IS NULL
		`, memberID).
		Joins("LEFT JOIN product_kinds AS kinds ON kinds.id = products.pro_kind_id")

	// 如果不包含下架課程，則過濾掉下架的課程
	if !includeOffline {
		query = query.Where("(products.status = 'Y' AND (pro_end_at IS NULL OR pro_end_at > CURRENT_TIMESTAMP()))")
	}

	if err := query.Group("products.id").
		Order("products.sorting ASC").
		Find(&overviews).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"msg":   "無法取得總覽資料",
			"error": err.Error(),
		})
		return
	}

	// 處理資料並建立匯出結構
	exportData := []MemberOverviewExport{}
	categoryMap := make(map[uint]*MemberOverviewExport)

	for _, overview := range overviews {
		// 找到對應的類別關係
		var kind *ProKindRelation
		for _, k := range proKinds {
			if k.KindID == overview.KindID {
				kind = &k
				break
			}
		}

		if kind == nil {
			// 如果找不到類別關係，使用預設值
			kind = &ProKindRelation{
				KindID:   overview.KindID,
				RootID:   overview.KindID,
				KindName: "未分類",
			}
		}

		// 找到根類別
		var rootKind *ProKindRelation
		rootID := kind.RootID
		if rootID == 0 {
			rootID = overview.KindID
		}

		for _, k := range proKinds {
			if k.KindID == rootID {
				rootKind = &k
				break
			}
		}

		if rootKind == nil {
			rootKind = kind
		}

		// 檢查是否已存在該類別的匯出資料
		if exportItem, exists := categoryMap[rootID]; exists {
			// 更新現有資料
			exportItem.PurchaseCount += int(overview.BuyTimes)
			if overview.BuyTimes > 0 {
				exportItem.PurchasedCourses++
				if overview.BuyAt > exportItem.LatestPurchase {
					exportItem.LatestPurchase = overview.BuyAt
				}
			}
			exportItem.TotalCourses++
		} else {
			// 建立新的匯出項目
			exportItem := &MemberOverviewExport{
				CategoryName:   rootKind.KindName,
				PurchaseCount:  int(overview.BuyTimes),
				TotalCourses:   1,
				LatestPurchase: overview.BuyAt,
			}

			if overview.BuyTimes > 0 {
				exportItem.PurchasedCourses = 1
			}

			categoryMap[rootID] = exportItem
		}
	}

	// 轉換為切片並計算完成率
	for _, item := range categoryMap {
		if item.TotalCourses > 0 {
			rate := float64(item.PurchasedCourses) / float64(item.TotalCourses) * 100
			item.CompletionRate = fmt.Sprintf("%.1f%%", rate)
		} else {
			item.CompletionRate = "0.0%"
		}
		item.HandleData()
		exportData = append(exportData, *item)
	}

	// 應用篩選條件
	if category != "" && category != "all" {
		filteredData := []MemberOverviewExport{}
		for _, item := range exportData {
			if item.CategoryName == category {
				filteredData = append(filteredData, item)
			}
		}
		exportData = filteredData
	}

	// 應用排序
	sort.Slice(exportData, func(i, j int) bool {
		var aVal, bVal interface{}
		switch sortBy {
		case "buy_times":
			aVal, bVal = exportData[i].PurchaseCount, exportData[j].PurchaseCount
		case "buy_at":
			aVal, bVal = exportData[i].LatestPurchase, exportData[j].LatestPurchase
		case "kind_name":
			aVal, bVal = exportData[i].CategoryName, exportData[j].CategoryName
		default:
			aVal, bVal = exportData[i].CategoryName, exportData[j].CategoryName
		}

		if sortOrder == "desc" {
			switch v := aVal.(type) {
			case int:
				return v > bVal.(int)
			case string:
				return v > bVal.(string)
			}
		} else {
			switch v := aVal.(type) {
			case int:
				return v < bVal.(int)
			case string:
				return v < bVal.(string)
			}
		}
		return false
	})

	// 根據匯出類型決定匯出內容
	if exportType == "detail" {
		// 詳細匯出：匯出所有課程資料
		var purchasedCourses, unpurchasedCourses []MemberDetailExport
		var mainCategoryName string
		var totalPurchases, purchasedCount, totalCount int
		var latestPurchase string

		for _, overview := range overviews {
			// 找到對應的類別關係
			var kind *ProKindRelation
			for _, k := range proKinds {
				if k.KindID == overview.KindID {
					kind = &k
					break
				}
			}

			if kind == nil {
				kind = &ProKindRelation{
					KindID:   overview.KindID,
					RootID:   overview.KindID,
					KindName: "未分類",
				}
			}

			// 找到根類別
			var rootKind *ProKindRelation
			rootID := kind.RootID
			if rootID == 0 {
				rootID = overview.KindID
			}

			for _, k := range proKinds {
				if k.KindID == rootID {
					rootKind = &k
					break
				}
			}

			if rootKind == nil {
				rootKind = kind
			}

			// 應用詳細篩選
			if category != "" && category != "all" && rootKind.KindName != category {
				continue
			}

			if detailFilter != "" && detailFilter != "all" && kind.KindName != detailFilter {
				continue
			}

			// 設定主類別名稱（用於檔案名稱）
			if mainCategoryName == "" {
				mainCategoryName = rootKind.KindName
			}

			// 建立課程名稱（包含下架標示）
			courseName := overview.ProName
			if overview.ProStatus == "N" {
				courseName += " (下架)"
			}

			// 建立詳細匯出項目
			detailItem := MemberDetailExport{
				CategoryName:    rootKind.KindName,
				SubCategoryName: kind.KindName,
				CourseName:      courseName,
				PurchaseCount:   int(overview.BuyTimes),
				PurchaseDate:    overview.BuyAt,
			}

			// 統計資料
			totalCount++
			if overview.BuyTimes > 0 {
				detailItem.IsPurchased = "已購買"
				purchasedCount++
				totalPurchases += int(overview.BuyTimes)
				if overview.BuyAt > latestPurchase {
					latestPurchase = overview.BuyAt
				}
				detailItem.HandleData()
				purchasedCourses = append(purchasedCourses, detailItem)
			} else {
				detailItem.IsPurchased = "未購買"
				detailItem.HandleData()
				unpurchasedCourses = append(unpurchasedCourses, detailItem)
			}
		}

		// 對課程進行排序
		sortCourses := func(courses []MemberDetailExport) {
			sort.Slice(courses, func(i, j int) bool {
				return courses[i].CourseName < courses[j].CourseName
			})
		}

		sortCourses(purchasedCourses)
		sortCourses(unpurchasedCourses)

		// 建立統計摘要
		summaryData := []MemberCourseSummary{
			{StatType: "總購買次數", StatValue: fmt.Sprintf("%d", totalPurchases), StatNote: ""},
			{StatType: "已購買課程", StatValue: fmt.Sprintf("%d", purchasedCount), StatNote: ""},
			{StatType: "總課程數", StatValue: fmt.Sprintf("%d", totalCount), StatNote: ""},
		}

		completionRate := 0.0
		if totalCount > 0 {
			completionRate = float64(purchasedCount) / float64(totalCount) * 100
		}
		summaryData = append(summaryData, MemberCourseSummary{
			StatType:  "完成率",
			StatValue: fmt.Sprintf("%.1f%%", completionRate),
			StatNote:  "",
		})

		if latestPurchase != "" {
			summaryData = append(summaryData, MemberCourseSummary{
				StatType:  "最近購買",
				StatValue: ParseStrDate(latestPurchase, "2006/01/02 15:04"),
				StatNote:  "",
			})
		} else {
			summaryData = append(summaryData, MemberCourseSummary{
				StatType:  "最近購買",
				StatValue: "－",
				StatNote:  "",
			})
		}

		// 合併所有資料：統計摘要 + 已購買課程 + 未購買課程
		allData := []interface{}{}

		// 添加統計摘要
		for _, summary := range summaryData {
			allData = append(allData, summary)
		}

		// 添加分隔行
		allData = append(allData, MemberCourseSummary{StatType: "", StatValue: "", StatNote: ""})
		allData = append(allData, MemberCourseSummary{StatType: "已購買課程", StatValue: "", StatNote: ""})

		// 添加已購買課程
		for _, course := range purchasedCourses {
			allData = append(allData, course)
		}

		// 添加分隔行
		allData = append(allData, MemberCourseSummary{StatType: "", StatValue: "", StatNote: ""})
		allData = append(allData, MemberCourseSummary{StatType: "未購買課程", StatValue: "", StatNote: ""})

		// 添加未購買課程
		for _, course := range unpurchasedCourses {
			allData = append(allData, course)
		}

		// 設定檔案名稱
		fileName := fmt.Sprintf("%s-學習總覽.csv", member.Name)
		if mainCategoryName != "" {
			fileName = fmt.Sprintf("%s-%s.csv", member.Name, mainCategoryName)
		}

		// 匯出詳細 CSV（包含統計摘要）
		ExportMemberDetailToCSV(c, allData, fileName)
	} else {
		// 一般匯出：匯出所有類別的詳細資料，按類別分頁
		ExportMemberOverviewDetailed(c, member, overviews, proKinds, category, sortBy, sortOrder, includeOffline)
	}
}

// 匯出會員總覽詳細資料（按類別分頁）
func ExportMemberOverviewDetailed(c *gin.Context, member Member, overviews []MemProOverview, proKinds []ProKindRelation, category, sortBy, sortOrder string, includeOffline bool) {
	// 按主類別分組資料
	categoryData := make(map[string][]MemberDetailExport)
	categoryStats := make(map[string]map[string]interface{})

	for _, overview := range overviews {
		// 找到對應的類別關係
		var kind *ProKindRelation
		for _, k := range proKinds {
			if k.KindID == overview.KindID {
				kind = &k
				break
			}
		}

		if kind == nil {
			kind = &ProKindRelation{
				KindID:   overview.KindID,
				RootID:   overview.KindID,
				KindName: "未分類",
			}
		}

		// 找到根類別
		var rootKind *ProKindRelation
		rootID := kind.RootID
		if rootID == 0 {
			rootID = overview.KindID
		}

		for _, k := range proKinds {
			if k.KindID == rootID {
				rootKind = &k
				break
			}
		}

		if rootKind == nil {
			rootKind = kind
		}

		// 應用類別篩選
		if category != "" && category != "all" && rootKind.KindName != category {
			continue
		}

		categoryName := rootKind.KindName

		// 初始化類別資料
		if categoryData[categoryName] == nil {
			categoryData[categoryName] = []MemberDetailExport{}
			categoryStats[categoryName] = map[string]interface{}{
				"totalPurchases":   0,
				"purchasedCourses": 0,
				"totalCourses":     0,
				"latestPurchase":   "",
			}
		}

		// 建立課程名稱（包含下架標示）
		courseName := overview.ProName
		if overview.ProStatus == "N" {
			courseName += " (下架)"
		}

		// 建立詳細匯出項目
		detailItem := MemberDetailExport{
			CategoryName:    rootKind.KindName,
			SubCategoryName: kind.KindName,
			CourseName:      courseName,
			PurchaseCount:   int(overview.BuyTimes),
			PurchaseDate:    overview.BuyAt,
		}

		if overview.BuyTimes > 0 {
			detailItem.IsPurchased = "已購買"
			categoryStats[categoryName]["purchasedCourses"] = categoryStats[categoryName]["purchasedCourses"].(int) + 1
			categoryStats[categoryName]["totalPurchases"] = categoryStats[categoryName]["totalPurchases"].(int) + int(overview.BuyTimes)
			if overview.BuyAt > categoryStats[categoryName]["latestPurchase"].(string) {
				categoryStats[categoryName]["latestPurchase"] = overview.BuyAt
			}
		} else {
			detailItem.IsPurchased = "未購買"
		}

		categoryStats[categoryName]["totalCourses"] = categoryStats[categoryName]["totalCourses"].(int) + 1
		detailItem.HandleData()
		categoryData[categoryName] = append(categoryData[categoryName], detailItem)
	}

	// 對每個類別的課程進行排序
	for categoryName, courses := range categoryData {
		// 分離已購買和未購買課程
		var purchasedCourses, unpurchasedCourses []MemberDetailExport
		for _, course := range courses {
			if course.IsPurchased == "已購買" {
				purchasedCourses = append(purchasedCourses, course)
			} else {
				unpurchasedCourses = append(unpurchasedCourses, course)
			}
		}

		// 對課程進行排序
		sortCourses := func(courses []MemberDetailExport) {
			sort.Slice(courses, func(i, j int) bool {
				return courses[i].CourseName < courses[j].CourseName
			})
		}

		sortCourses(purchasedCourses)
		sortCourses(unpurchasedCourses)

		// 重新組合：已購買在前，未購買在後
		categoryData[categoryName] = append(purchasedCourses, unpurchasedCourses...)
	}

	// 使用 Excel 匯出（支援多個分頁）
	ExportMemberOverviewToExcel(c, member, categoryData, categoryStats)
}

// 匯出會員總覽到 Excel（多分頁）
func ExportMemberOverviewToExcel(c *gin.Context, member Member, categoryData map[string][]MemberDetailExport, categoryStats map[string]map[string]interface{}) {
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Printf("Error to close file: %+v\n", err)
		}
	}()

	// 刪除預設的 Sheet1
	f.DeleteSheet("Sheet1")

	// 為每個類別創建分頁
	for categoryName, courses := range categoryData {
		sheetName := categoryName
		if len(sheetName) > 31 { // Excel 分頁名稱限制
			sheetName = sheetName[:31]
		}

		// 創建分頁
		f.NewSheet(sheetName)

		// 設定樣式
		styleID, _ := f.NewStyle(&excelize.Style{
			Font: &excelize.Font{
				Size:   12,
				Family: "新細明體",
			},
		})

		headerStyleID, _ := f.NewStyle(&excelize.Style{
			Font: &excelize.Font{
				Size:   12,
				Family: "新細明體",
				Bold:   true,
			},
			Fill: &excelize.Fill{
				Type:    "pattern",
				Color:   []string{"#E6E6FA"},
				Pattern: 1,
			},
		})

		// 設定欄寬
		f.SetColWidth(sheetName, "A", "A", 20) // 主類別
		f.SetColWidth(sheetName, "B", "B", 20) // 子類別
		f.SetColWidth(sheetName, "C", "C", 30) // 課程名稱
		f.SetColWidth(sheetName, "D", "D", 12) // 購買狀態
		f.SetColWidth(sheetName, "E", "E", 12) // 購買次數
		f.SetColWidth(sheetName, "F", "F", 20) // 購買時間

		// 寫入統計摘要
		stats := categoryStats[categoryName]
		f.SetCellValue(sheetName, "A1", "統計摘要")
		f.SetCellStyle(sheetName, "A1", "A1", headerStyleID)

		f.SetCellValue(sheetName, "A2", "總購買次數")
		f.SetCellValue(sheetName, "B2", stats["totalPurchases"])
		f.SetCellValue(sheetName, "A3", "已購買課程")
		f.SetCellValue(sheetName, "B3", stats["purchasedCourses"])
		f.SetCellValue(sheetName, "A4", "總課程數")
		f.SetCellValue(sheetName, "B4", stats["totalCourses"])

		completionRate := 0.0
		if stats["totalCourses"].(int) > 0 {
			completionRate = float64(stats["purchasedCourses"].(int)) / float64(stats["totalCourses"].(int)) * 100
		}
		f.SetCellValue(sheetName, "A5", "完成率")
		f.SetCellValue(sheetName, "B5", fmt.Sprintf("%.1f%%", completionRate))

		f.SetCellValue(sheetName, "A6", "最近購買")
		latestPurchase := stats["latestPurchase"].(string)
		if latestPurchase != "" {
			f.SetCellValue(sheetName, "B6", ParseStrDate(latestPurchase, "2006/01/02 15:04"))
		} else {
			f.SetCellValue(sheetName, "B6", "－")
		}

		// 設定統計區域樣式
		f.SetCellStyle(sheetName, "A2", "B6", styleID)

		// 寫入課程資料標題
		row := 8
		headers := []string{"主類別", "子類別", "課程名稱", "購買狀態", "購買次數", "購買時間"}
		for i, header := range headers {
			cell := fmt.Sprintf("%c%d", 'A'+i, row)
			f.SetCellValue(sheetName, cell, header)
			f.SetCellStyle(sheetName, cell, cell, headerStyleID)
		}

		// 寫入課程資料
		row++
		for _, course := range courses {
			f.SetCellValue(sheetName, fmt.Sprintf("A%d", row), course.CategoryName)
			f.SetCellValue(sheetName, fmt.Sprintf("B%d", row), course.SubCategoryName)
			f.SetCellValue(sheetName, fmt.Sprintf("C%d", row), course.CourseName)
			f.SetCellValue(sheetName, fmt.Sprintf("D%d", row), course.IsPurchased)
			f.SetCellValue(sheetName, fmt.Sprintf("E%d", row), course.PurchaseCount)
			f.SetCellValue(sheetName, fmt.Sprintf("F%d", row), course.PurchaseDate)

			// 設定樣式
			f.SetCellStyle(sheetName, fmt.Sprintf("A%d", row), fmt.Sprintf("F%d", row), styleID)
			row++
		}
	}

	// 將Excel檔案緩存在記憶體中
	excelBytes, _ := f.WriteToBuffer()
	fileBytes, _ := io.ReadAll(excelBytes)

	// 設定檔案名稱
	fileName := fmt.Sprintf("%s-學習總覽詳細.xlsx", member.Name)
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", fileName))
	c.Data(http.StatusOK, "application/octet-stream", fileBytes)
}

// 匯出會員詳細資料到 CSV（包含統計摘要）
func ExportMemberDetailToCSV(c *gin.Context, allData []interface{}, fileName string) {
	var csvContent strings.Builder

	// 寫入 BOM 以支援中文
	csvContent.WriteString("\xEF\xBB\xBF")

	// 處理混合資料類型的匯出
	for i, data := range allData {
		if i == 0 {
			// 寫入標題行（根據第一個資料項目的類型）
			switch data.(type) {
			case MemberCourseSummary:
				csvContent.WriteString("統計項目,統計值,備註\n")
			case MemberDetailExport:
				csvContent.WriteString("主類別,子類別,課程名稱,購買狀態,購買次數,購買時間\n")
			}
		}

		// 寫入資料行
		switch v := data.(type) {
		case MemberCourseSummary:
			csvContent.WriteString(fmt.Sprintf("\"%s\",\"%s\",\"%s\"\n",
				strings.ReplaceAll(v.StatType, "\"", "\"\""),
				strings.ReplaceAll(v.StatValue, "\"", "\"\""),
				strings.ReplaceAll(v.StatNote, "\"", "\"\"")))
		case MemberDetailExport:
			csvContent.WriteString(fmt.Sprintf("\"%s\",\"%s\",\"%s\",\"%s\",%d,\"%s\"\n",
				strings.ReplaceAll(v.CategoryName, "\"", "\"\""),
				strings.ReplaceAll(v.SubCategoryName, "\"", "\"\""),
				strings.ReplaceAll(v.CourseName, "\"", "\"\""),
				strings.ReplaceAll(v.IsPurchased, "\"", "\"\""),
				v.PurchaseCount,
				strings.ReplaceAll(v.PurchaseDate, "\"", "\"\"")))
		}
	}

	// 設定回應標頭
	c.Header("Content-Type", "text/csv; charset=utf-8")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", fileName))

	// 回傳 CSV 內容
	c.String(http.StatusOK, csvContent.String())
}

func GetProductContent(c *gin.Context) {
	proCont := ProductContent{}

	if err := c.ShouldBind(&proCont); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "資料錯誤",
			"error": err.Error(),
		})
		return
	}

	conn := ConnectDB()
	defer CloseDB(conn)

	if err := proCont.GetContentByKindID(uint(proCont.ProKindID.Int64)); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "查無資料",
			"error": err.Error(),
		})
		return
	}

	conn.Model(&Admin{}).Select("admin_name").Find(&proCont.CreatedBy, proCont.CreatedByID)
	conn.Model(&Admin{}).Select("admin_name").Find(&proCont.UpdatedBy, proCont.UpdatedByID)

	c.JSON(http.StatusOK, gin.H{
		"data": proCont,
	})
}

func UpdateProductContent(c *gin.Context) {
	proCont := ProductContent{}

	if err := c.ShouldBind(&proCont); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "資料錯誤",
			"error": err.Error(),
		})
		return
	}

	info := GetAdminInfo(c)

	if proCont.ID == 0 {
		proCont.CreatedByID = info.ID
	}
	proCont.UpdatedByID = info.ID

	if proCont.ProKindID.Int64 == 0 {
		proCont.ProKindID = null.NewInt(0, false)
	}

	conn := ConnectDB()
	defer CloseDB(conn)

	conn = conn.Begin()

	if err := conn.Save(&proCont).Error; err != nil {
		conn.Rollback()
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "更新失敗",
			"error": err.Error(),
		})
		return
	}

	conn.Commit()

	c.JSON(http.StatusOK, gin.H{
		"msg": "更新成功",
	})
}

func UpdateMemberProductCounselDeadline(c *gin.Context) {
	memProID := c.Param("mem_pro_id")
	deadlineAt := c.PostForm("counsel_deadline_at")

	conn := ConnectDB()
	defer CloseDB(conn)

	t, _ := time.Parse("2006/01/02 15:04", deadlineAt)
	if err := conn.Select("counsel_deadline_at").Where("id = ?", memProID).
		Updates(&MemberProduct{
			CounselDeadlineAt: null.NewTime(t, !t.IsZero()),
		}).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "更新失敗",
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"msg": "更新成功",
	})
}

func DeleteProductOrders(c *gin.Context) {
	data := struct {
		IDs []uint `json:"ids"`
	}{}

	if err := c.ShouldBind(&data); err != nil || len(data.IDs) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "無法取得要刪除的資料",
			"error": err.Error(),
		})
		return
	}

	conn := ConnectDB()
	defer CloseDB(conn)

	conn = conn.Begin()

	if err := conn.Where("id IN ?", data.IDs).Delete(&MemberProduct{}).Error; err != nil {
		conn.Rollback()
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "刪除失敗",
			"error": err.Error(),
		})
		return
	}

	conn.Commit()

	c.JSON(http.StatusOK, gin.H{
		"msg": "刪除成功",
	})
}

func GetProductReports(c *gin.Context) {
	var data []ProductReport

	proIDs := c.QueryArray("pro_constraint[]")

	conn := ConnectDB()
	defer CloseDB(conn)

	if len(proIDs) > 0 {
		conn = conn.Where("products.id IN (?)", proIDs)
	}

	if err := conn.Model(&MemberProduct{}).Select("products.id AS pro_id", "products.pro_name", "products.pro_kind_id", "product_kinds.kind_name",
		"members.id AS member_id", "members.name AS mem_name", "member_products.points", "member_products.created_at AS buy_at").
		Joins("JOIN products ON products.id = member_products.product_id").
		Joins("JOIN product_kinds ON product_kinds.id = products.pro_kind_id").
		Joins("JOIN members ON members.id = member_products.member_id").
		Where("member_products.is_review = 'N'").
		Where("DATE(member_products.created_at) BETWEEN ? AND ?", c.Query("start_at"), c.Query("end_at")).
		Order("member_products.created_at DESC").
		Find(&data).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"msg":   "無法取得資料",
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"msg":  "查詢成功",
		"data": data,
	})
}
