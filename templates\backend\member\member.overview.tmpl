<!DOCTYPE html>
<html>
    <head>
        {{template "head" .}}
        {{template "sweet_alert" .}}
    </head>
    <body>
        {{template "top" .}}

        {{template "admin/member.overview" .}}

        {{template "bottom" .}}
    </body>
</html>

<script>
    createApp({
        delimiters: ['${', '}'],
        mixins: [CommonMixin, MemInfo, MemReg],
        data() {
            return {
                index: 'overview',
                item: {},
                items: [],
                filteredItems: [],
                pros: {},
                proKinds: [],
                proKindID: 0,
                selectedCategory: 'all',
                sortBy: 'sorting',
                sortOrder: 'asc',
                categoryStats: {},
                detailFilter: 'all', // 詳細視窗的過濾選項
                includeOfflineCourses: false, // 是否包含下架課程
            }
        },
        async mounted() {
            await this.getProKinds()
            this.getOverview()
        },
        methods: {
            getProKinds() {
                return axios.get('/api/admin/products/kind/relations')
                    .then(res => {
                        this.proKinds = res.data.data
                    })
                    .catch(err => {
                        console.log(err)
                        msgError(err?.response?.data?.msg ?? err)
                    })
            },
            getOverview() {
                axios.get(`/api/admin/members/overview/${this.member.id}`)
                    .then(res => {
                        for (let d of res.data.data) {
                            let kind = this.proKinds.find(k => k.kind_id === d.kind_id),
                                root = this.proKinds.find(k => k.kind_id === kind?.root_id),
                                rootID = kind?.root_id ?? d.kind_id
                            
                            let item = this.items.find(i => i.kind_id === rootID)

                            if (!item) {
                                item = {
                                    kind_id: rootID,
                                    kind_name: root?.kind_name ?? kind?.kind_name,
                                    buy_times: d.buy_times,
                                    buy_at: d.buy_at,
                                    sorting: root.sorting,
                                    pros: {
                                        y: [],
                                        n: [],
                                    },
                                }
                                this.items.push(item)
                            }
                            else {
                                item.buy_times += d.buy_times
                                if (item.buy_at < d.buy_at) {
                                    item.buy_at = d.buy_at
                                }
                            }

                            if (d.buy_times > 0) {
                                item.pros.y.push({
                                    pro_name: d.pro_name,
                                    kind_id: d.kind_id,
                                    kind_name: kind?.kind_name,
                                    buy_times: d.buy_times,
                                    buy_at: d.buy_at,
                                    pro_status: d.pro_status,
                                    sorting: kind.sorting,
                                })
                            }
                            else {
                                item.pros.n.push({
                                    pro_name: d.pro_name,
                                    kind_id: d.kind_id,
                                    kind_name: kind?.kind_name,
                                    pro_status: d.pro_status,
                                    sorting: kind.sorting,
                                })
                            }
                        }

                        this.items.sort((a, b) => a.sorting - b.sorting)

                        for (let item of this.items) {
                            // 對已購買課程進行分群排序
                            item.pros.y = this.groupAndSortCourses(item.pros.y, item.kind_id)
                            // 對未購買課程進行分群排序
                            item.pros.n = this.groupAndSortCourses(item.pros.n, item.kind_id)
                        }

                        this.calculateCategoryStats()
                        this.applyFilters()
                    })
                    .catch(err => {
                        console.log(err)
                        msgError(err?.response?.data?.msg ?? err)
                    })
            },
            showPros(item) {
                $.fancybox.open({
                    src: '#pros',
                    type: 'inline',
                    opts: {
                        touch: false,
                        smallBtn: true,
                        toolbar: false,
                        clickSlide: 'close',
                        clickOutside: 'close',
                        beforeShow: () => {
                            this.item = item
                            this.detailFilter = 'all' // 重置過濾器
                        },
                    }
                })
            },
            calculateCategoryStats() {
                this.categoryStats = {}
                for (let item of this.items) {
                    const categoryName = item.kind_name
                    if (!this.categoryStats[categoryName]) {
                        this.categoryStats[categoryName] = {
                            totalCourses: 0,
                            totalPurchases: 0,
                            purchasedCourses: 0,
                            unpurchasedCourses: 0
                        }
                    }
                    this.categoryStats[categoryName].totalCourses += (item.pros.y.length + item.pros.n.length)
                    this.categoryStats[categoryName].totalPurchases += item.buy_times
                    this.categoryStats[categoryName].purchasedCourses += item.pros.y.length
                    this.categoryStats[categoryName].unpurchasedCourses += item.pros.n.length
                }
            },
            applyFilters() {
                let filtered = [...this.items]

                // 分類篩選
                if (this.selectedCategory !== 'all') {
                    filtered = filtered.filter(item => item.kind_name === this.selectedCategory)
                }

                // 應用下架課程篩選
                if (!this.includeOfflineCourses) {
                    filtered = filtered.map(item => {
                        const newItem = { ...item }
                        // 過濾已購買課程中的下架課程
                        if (newItem.pros && newItem.pros.y) {
                            newItem.pros.y = this.filterOfflineCourses(newItem.pros.y)
                        }
                        // 過濾未購買課程中的下架課程
                        if (newItem.pros && newItem.pros.n) {
                            newItem.pros.n = this.filterOfflineCourses(newItem.pros.n)
                        }
                        return newItem
                    })
                }

                // 排序
                filtered.sort((a, b) => {
                    let aVal, bVal
                    switch (this.sortBy) {
                        case 'buy_times':
                            aVal = a.buy_times
                            bVal = b.buy_times
                            break
                        case 'buy_at':
                            aVal = new Date(a.buy_at)
                            bVal = new Date(b.buy_at)
                            break
                        case 'kind_name':
                            aVal = a.kind_name
                            bVal = b.kind_name
                            break
                        default:
                            aVal = a.sorting
                            bVal = b.sorting
                    }

                    if (this.sortOrder === 'desc') {
                        return aVal < bVal ? 1 : -1
                    }
                    return aVal > bVal ? 1 : -1
                })

                this.filteredItems = filtered
            },
            filterOfflineCourses(courseList) {
                if (!courseList) return []

                // 直接過濾，保持原有結構
                return courseList.filter(course => {
                    if (course.isGroupSeparator) return true
                    return course.pro_status !== 'N'
                })
            },
            onCategoryChange() {
                this.applyFilters()
            },
            onSortChange() {
                this.applyFilters()
            },
            onOfflineCoursesChange() {
                this.applyFilters()
            },
            getCategoryOptions() {
                const categories = ['all', ...new Set(this.items.map(item => item.kind_name))]
                return categories.map(cat => ({
                    value: cat,
                    label: cat === 'all' ? '全部類別' : cat
                }))
            },
            groupAndSortCourses(courses, parentKindId) {
                if (!courses || courses.length === 0) return []

                // 按課程子類別進行分群，如果沒有子類別，則使用「其他」作為群組
                const groups = {}

                courses.forEach(course => {
                    let groupKey = course.kind_name

                    // 如果沒有子類別，使用「其他」作為群組
                    if (!groupKey) {
                        groupKey = '其他'
                    }

                    if (!groups[groupKey]) {
                        groups[groupKey] = []
                    }
                    groups[groupKey].push(course)
                })

                // 對每個群組內的課程進行排序
                Object.keys(groups).forEach(groupKey => {
                    groups[groupKey].sort((a, b) => {
                        // 優先顯示與父類別相同的課程
                        if (a.kind_id === parentKindId && b.kind_id !== parentKindId) return -1
                        if (a.kind_id !== parentKindId && b.kind_id === parentKindId) return 1

                        // 按排序號排序
                        // if (a.sorting !== b.sorting) return a.sorting - b.sorting

                        // 按課程名稱排序
                        return a.pro_name.localeCompare(b.pro_name, undefined, { numeric: true, sensitivity: 'base' })
                    })
                })

                // 將分組後的課程合併，並在群組間添加分隔
                const result = []
                const sortedGroupKeys = Object.keys(groups).sort()

                sortedGroupKeys.forEach((groupKey, index) => {
                    // 只有在有多個群組時才添加分隔符
                    if (sortedGroupKeys.length > 1) {
                        // 計算該群組的統計資訊
                        const groupCourses = groups[groupKey]
                        const courseCount = groupCourses.length
                        const totalPurchases = groupCourses.reduce((sum, course) => sum + (course.buy_times || 0), 0)

                        // 添加群組分隔標記
                        result.push({
                            isGroupSeparator: true,
                            groupName: groupKey,
                            courseCount: courseCount,
                            totalPurchases: totalPurchases
                        })
                    }
                    result.push(...groups[groupKey])
                })

                return result
            },
            getDetailSubCategories() {
                if (!this.item || !this.item.pros) return []

                const allCourses = [...(this.item.pros.y || []), ...(this.item.pros.n || [])]
                const subCategories = [...new Set(allCourses
                    .filter(course => !course.isGroupSeparator)
                    .map(course => course.kind_name))]

                return ['all', ...subCategories.sort()]
            },
            getFilteredCourses(courseList) {
                if (!courseList) return []

                // 先根據下架課程設定進行過濾
                let filteredByOffline = courseList
                if (!this.includeOfflineCourses) {
                    filteredByOffline = courseList.filter(course => {
                        if (course.isGroupSeparator) return true
                        return course.pro_status !== 'N'
                    })
                }

                // 如果沒有子類別篩選，直接返回下架過濾後的結果
                if (this.detailFilter === 'all') return filteredByOffline

                const filteredCourses = []
                let currentGroupSeparator = null
                let groupCourses = []

                for (let i = 0; i < filteredByOffline.length; i++) {
                    const course = filteredByOffline[i]

                    if (course.isGroupSeparator) {
                        // 處理前一個群組
                        if (groupCourses.length > 0) {
                            // 如果有群組分隔符且群組中有符合條件的課程，先加入分隔符
                            if (currentGroupSeparator) {
                                filteredCourses.push(currentGroupSeparator)
                            }
                            // 加入群組中的課程
                            filteredCourses.push(...groupCourses)
                        }

                        // 開始新群組
                        currentGroupSeparator = course
                        groupCourses = []
                    } else {
                        // 檢查課程是否符合篩選條件
                        if (course.kind_name === this.detailFilter) {
                            groupCourses.push(course)
                        }
                    }
                }

                // 處理最後一個群組
                if (groupCourses.length > 0) {
                    if (currentGroupSeparator) {
                        filteredCourses.push(currentGroupSeparator)
                    }
                    filteredCourses.push(...groupCourses)
                }

                return filteredCourses
            },
            onDetailFilterChange() {
                // 過濾器改變時不需要特別處理，Vue會自動更新顯示
            },
            getFilteredStats() {
                if (!this.item || !this.item.pros) return {
                    totalPurchases: 0,
                    purchasedCount: 0,
                    unpurchasedCount: 0,
                    totalCount: 0,
                    completionRate: 0,
                    latestPurchaseDate: null
                }

                const filteredPurchased = this.getFilteredCourses(this.item.pros.y)?.filter(c => !c.isGroupSeparator) || []
                const filteredUnpurchased = this.getFilteredCourses(this.item.pros.n)?.filter(c => !c.isGroupSeparator) || []

                const totalPurchases = filteredPurchased.reduce((sum, course) => sum + (course.buy_times || 0), 0)
                const purchasedCount = filteredPurchased.length
                const unpurchasedCount = filteredUnpurchased.length
                const totalCount = purchasedCount + unpurchasedCount
                const completionRate = totalCount > 0 ? Math.round((purchasedCount / totalCount) * 100) : 0

                // 計算過濾後的最近購買時間
                let latestPurchaseDate = null
                if (filteredPurchased.length > 0) {
                    const purchaseDates = filteredPurchased
                        .filter(course => course.buy_at)
                        .map(course => new Date(course.buy_at))
                        .filter(date => !isNaN(date.getTime()))

                    if (purchaseDates.length > 0) {
                        latestPurchaseDate = new Date(Math.max(...purchaseDates))
                    }
                }

                return {
                    totalPurchases,
                    purchasedCount,
                    unpurchasedCount,
                    totalCount,
                    completionRate,
                    latestPurchaseDate
                }
            },
            getProductCount(products) {
                return products?.filter(c => !c.isGroupSeparator)?.length || 0
            },
            exportOverview() {
                // 準備匯出參數
                const params = new URLSearchParams({
                    category: this.selectedCategory,
                    sort_by: this.sortBy,
                    sort_order: this.sortOrder,
                    include_offline: this.includeOfflineCourses
                })

                // 建立匯出 URL
                const exportUrl = `/api/admin/members/overview/${this.member.id}/export?${params.toString()}`

                // 開啟新視窗下載檔案
                window.open(exportUrl, '_blank')
            },
            exportDetailOverview() {
                if (!this.item || !this.item.kind_id) {
                    msgError('請先選擇要匯出的課程類別')
                    return
                }

                // 準備匯出參數
                const params = new URLSearchParams({
                    category: this.item.kind_name,
                    detail_filter: this.detailFilter,
                    export_type: 'detail',
                    include_offline: this.includeOfflineCourses
                })

                // 建立匯出 URL
                const exportUrl = `/api/admin/members/overview/${this.member.id}/export?${params.toString()}`

                // 開啟新視窗下載檔案
                window.open(exportUrl, '_blank')
            }
        }
    }).mount('#app')
</script>

<style>

    .category-filter-section {
        background: white;
        border-radius: 8px;
        padding: 15px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }

    .stats-card {
        background: #007bff;
        color: white;
        border-radius: 6px;
        padding: 12px;
        margin-bottom: 15px;
        border: 1px solid #0056b3;
    }

    .stats-item {
        text-align: center;
        padding: 8px;
        background: rgba(255,255,255,0.15);
        border-radius: 4px;
        margin: 0 3px;
    }

    .stats-number {
        font-size: 1.5rem;
        font-weight: bold;
        display: block;
    }

    .stats-label {
        font-size: 0.8rem;
        opacity: 0.9;
        margin-top: 5px;
    }

    .course-table {
        background: white;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .course-table thead th {
        background-color: #f8f9fa;
        padding: 15px;
        font-weight: 600;
    }

    .course-table tbody tr {
        transition: all 0.3s ease;
    }

    .course-table tbody tr:hover {
        background-color: #f8f9fa;
    }

    .course-table td {
        padding: 12px 15px;
        vertical-align: middle;
    }

    .category-badge {
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 500;
    }

    .purchase-count {
        padding: 4px 8px;
        border-radius: 15px;
        font-weight: bold;
    }

    .course-ratio {
        font-size: 0.9rem;
    }

    .detail-btn {
        border: none;
        padding: 6px 12px;
        border-radius: 4px;
    }

    .purchased-card {
        border-left: 4px solid #28a745;
    }

    .unpurchased-card {
        border-left: 4px solid #ffc107;
    }

    .form-select-sm {
        border-radius: 20px;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .form-select-sm:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
    }

    .alert-info {
        background: linear-gradient(45deg, #17a2b8, #138496);
        color: white;
        border: none;
        border-radius: 10px;
    }

    .progress-ring {
        width: 60px;
        height: 60px;
        margin: 0 auto;
    }

    .progress-ring circle {
        fill: transparent;
        stroke: #e9ecef;
        stroke-width: 4;
    }

    .progress-ring .progress {
        stroke: #28a745;
        stroke-linecap: round;
        transition: stroke-dasharray 0.5s ease;
    }
</style>

{{define "admin/member.overview"}}
    <div id="app" class="content_box">
        {{template "member.info" .}}

        <div class="card">
            <div class="card-header category-filter-section">
                <div class="d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        學習總覽
                    </h4>
                    <div class="d-flex gap-3">
                        <div class="d-flex align-items-center">
                            <label class="form-label me-2 mb-0 fw-bold">類別篩選：</label>
                            <select v-model="selectedCategory" @change="onCategoryChange" class="form-select form-select-sm" style="width: auto;">
                                <option v-for="option in getCategoryOptions()" :key="option.value" :value="option.value">
                                    ${ option.label }
                                </option>
                            </select>
                        </div>
                        <div class="d-flex align-items-center">
                            <label class="form-label me-2 mb-0 fw-bold">排序：</label>
                            <select v-model="sortBy" @change="onSortChange" class="form-select form-select-sm me-2" style="width: auto;">
                                <option value="sorting">預設排序</option>
                                <option value="kind_name">類別名稱</option>
                                <option value="buy_times">購買次數</option>
                                <option value="buy_at">最近購買時間</option>
                            </select>
                            <select v-model="sortOrder" @change="onSortChange" class="form-select form-select-sm" style="width: auto;">
                                <option value="asc">升序</option>
                                <option value="desc">降序</option>
                            </select>
                        </div>
                        <div class="d-flex align-items-center gap-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" v-model="includeOfflineCourses" @change="onOfflineCoursesChange" id="includeOfflineCourses">
                                <label class="form-check-label" for="includeOfflineCourses">
                                    顯示下架課程
                                </label>
                            </div>
                            <button type="button" @click="exportOverview" class="btn btn-outline-success btn-sm">
                                <i class="fas fa-download me-1"></i>匯出詳細 Excel
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table_wrapper table-sticky">
                    <table class="table course-table" cellspacing="0" border="0">
                        <thead>
                            <tr>
                                <th>課程類別</th>
                                <th>購買次數</th>
                                <th>子課程數量</th>
                                <th>最近購買時間</th>
                                <th>詳細</th>
                            </tr>
                        </thead>
                        <tbody>
                            <template v-for="item in filteredItems">
                                <tr>
                                    <td>
                                        <span>${ item.kind_name }</span>
                                    </td>
                                    <td>
                                        <span class="purchase-count">${ addCommas(item.buy_times) }</span>
                                    </td>
                                    <td class="course-ratio">
                                        <span class="text-success fw-bold">${ getProductCount(item.pros?.y) }</span> /
                                        <span class="text-muted">
                                            ${ getProductCount(item.pros?.y) + getProductCount(item.pros?.n) }
                                        </span>
                                        <div class="small text-muted">已購買 / 總數</div>
                                        <div class="progress mt-1" style="height: 4px;">
                                            <div class="progress-bar bg-success"
                                                 :style="{width: ((getProductCount(item.pros?.y) / (getProductCount(item.pros?.y) + getProductCount(item.pros?.n))) * 100) + '%'}">
                                            </div>
                                        </div>
                                        <div class="small text-muted">
                                            ${ Math.round((getProductCount(item.pros?.y) / (getProductCount(item.pros?.y) + getProductCount(item.pros?.n))) * 100 || 0).toFixed(0) }%
                                        </div>
                                    </td>
                                    </td>
                                    <td>
                                        <div class="fw-bold">${ formatDateTime(item.buy_at, false, '/') }</div>
                                        <div class="fw-bold" v-if="isZeroDate(item.buy_at)">－</div>
                                    </td>
                                    <td>
                                        <button type="button" @click="showPros(item)" class="btn detail-btn">
                                            <i class="fas fa-eye me-1"></i>
                                        </button>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div id="pros" class="card text-nowrap" style="display:none; width:-webkit-fill-available">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h4 class="fw-bold mb-0">${ item.kind_name } - 詳細資訊</h4>
                    <div class="d-flex align-items-center gap-3">
                        <div class="d-flex align-items-center">
                            <label class="form-label me-2 mb-0">子類別篩選：</label>
                            <select v-model="detailFilter" @change="onDetailFilterChange" class="form-select form-select-sm" style="width: 150px;">
                                <option value="all">全部子類別</option>
                                <option v-for="subCat in getDetailSubCategories().slice(1)" :key="subCat" :value="subCat">
                                    ${ subCat }
                                </option>
                            </select>
                        </div>
                        <div class="d-flex align-items-center gap-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" v-model="includeOfflineCourses" @change="onOfflineCoursesChange" id="includeOfflineCoursesDetail">
                                <label class="form-check-label" for="includeOfflineCoursesDetail">
                                    顯示下架課程
                                </label>
                            </div>
                            <button type="button" @click="exportDetailOverview" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-download me-1"></i>匯出詳細 CSV
                            </button>
                        </div>
                    </div>
                </div>
                <!-- 統計摘要 -->
                <div class="row text-center mb-3">
                    <div class="col-2">
                        <div class="bg-light p-2 rounded">
                            <strong>${ detailFilter === 'all' ? item.buy_times : getFilteredStats().totalPurchases }</strong>
                            <div class="small text-muted">總購買次數</div>
                        </div>
                    </div>
                    <div class="col-2">
                        <div class="bg-light p-2 rounded">
                            <strong>${ detailFilter === 'all' ? getProductCount(item.pros?.y) : getFilteredStats().purchasedCount }</strong>
                            <div class="small text-muted">已購買課程</div>
                        </div>
                    </div>
                    <div class="col-2">
                        <div class="bg-light p-2 rounded">
                            <strong>${ detailFilter === 'all' ? getProductCount(item.pros?.n) : getFilteredStats().unpurchasedCount }</strong>
                            <div class="small text-muted">未購買課程</div>
                        </div>
                    </div>
                    <div class="col-2">
                        <div class="bg-light p-2 rounded">
                            <strong>${ detailFilter === 'all' ? (getProductCount(item.pros?.y) + getProductCount(item.pros?.n)) : getFilteredStats().totalCount }</strong>
                            <div class="small text-muted">總課程數</div>
                        </div>
                    </div>
                    <div class="col-2">
                        <div class="bg-light p-2 rounded">
                            <strong>${ detailFilter === 'all' ? (Math.round((getProductCount(item.pros?.y) / (getProductCount(item.pros?.y) + getProductCount(item.pros?.n)) * 100) || 0)) : getFilteredStats().completionRate }%</strong>
                            <div class="small text-muted">完成率</div>
                        </div>
                    </div>
                    <div class="col-2">
                        <div class="bg-light p-2 rounded">
                            <strong v-if="detailFilter === 'all'">${ formatDateTime(item.buy_at, false, '/') }</strong>
                            <strong v-else-if="getFilteredStats().latestPurchaseDate">${ formatDateTime(getFilteredStats().latestPurchaseDate.toISOString(), false, '/') }</strong>
                            <strong v-else>－</strong>
                            <div class="small text-muted">最近購買</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white d-flex justify-content-between">
                                <span>已購買課程</span>
                                <span class="badge bg-light text-success">${ getFilteredCourses(item.pros?.y)?.filter(c => !c.isGroupSeparator)?.length || 0 }</span>
                            </div>
                            <div class="card-body">
                                <div class="table_wrapper overflow-auto" style="max-height:400px">
                                    <table class="table table-hover table-sm" cellspacing="0" border="0">
                                        <thead class="bg-white position-sticky top-0">
                                            <tr>
                                                <th>課程名稱</th>
                                                <th v-if="detailFilter === 'all'">子類別</th>
                                                <th>購買次數</th>
                                                <th>最近購買時間</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <template v-for="pro in getFilteredCourses(item.pros?.y)">
                                                <tr v-if="pro.isGroupSeparator" class="table-secondary">
                                                    <td :colspan="detailFilter === 'all' ? 4 : 3" class="text-center fw-bold py-2">
                                                        <i class="fas fa-layer-group me-2"></i>${ pro.groupName }
                                                        <span class="badge bg-primary ms-2">${ pro.courseCount } 堂課程</span>
                                                        <span v-if="pro.totalPurchases > 0" class="badge bg-success ms-1">共 ${ addCommas(pro.totalPurchases) } 次購買</span>
                                                    </td>
                                                </tr>
                                                <tr v-else>
                                                    <td v-html="pro.pro_name" class="fw-bold"></td>
                                                    <td v-if="detailFilter === 'all'">
                                                        <span class="badge bg-secondary">${ pro.kind_name }</span>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-success">${ addCommas(pro.buy_times) }</span>
                                                    </td>
                                                    <td class="small">${ formatDateTime(pro.buy_at, false, '/') }</td>
                                                </tr>
                                            </template>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="card border-warning">
                            <div class="card-header bg-warning text-dark d-flex justify-content-between">
                                <span>未購買課程</span>
                                <span class="badge bg-light text-warning">
                                    ${ getFilteredCourses(item.pros?.n)?.filter(c => !c.isGroupSeparator)?.length || 0 }
                                </span>
                            </div>
                            <div class="card-body">
                                <div class="table_wrapper overflow-auto" style="max-height:400px;">
                                    <table class="table table-hover table-sm" cellspacing="0" border="0">
                                        <thead class="bg-white position-sticky top-0">
                                            <tr>
                                                <th>課程名稱</th>
                                                <th v-if="detailFilter === 'all'">子類別</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <template v-for="pro in getFilteredCourses(item.pros?.n)">
                                                <tr v-if="pro.isGroupSeparator" class="table-secondary">
                                                    <td :colspan="detailFilter === 'all' ? 2 : 1" class="text-center fw-bold py-2">
                                                        <i class="fas fa-layer-group me-2"></i>${ pro.groupName }
                                                        <span class="badge bg-primary ms-2">${ pro.courseCount } 堂課程</span>
                                                    </td>
                                                </tr>
                                                <tr v-else>
                                                    <td v-html="pro.pro_name" class="text-muted"></td>
                                                    <td v-if="detailFilter === 'all'">
                                                        <span class="badge bg-light text-dark">${ pro.kind_name }</span>
                                                    </td>
                                                </tr>
                                            </template>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{{end}}