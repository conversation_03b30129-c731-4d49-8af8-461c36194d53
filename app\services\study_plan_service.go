package services

import (
	"context"
	"cx/app/models"
	"cx/app/repository"
	"cx/app/utils"
	"errors"
	"fmt"
	"math"
	"strconv"
	"time"

	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

type StudyPlanService interface {
	GetPlan(ctx context.Context, memberID uint) (models.StudyPlan, error)
	GetPlans(ctx context.Context, page *models.PageOption, filter *models.StudyPlanFilter) ([]models.StudyPlan, error)
	GetStudyPlanOverview(ctx context.Context, page *models.PageOption, filter *models.StudyPlanFilter) (*models.StudyPlanOverview, error)
	CreatePlan(ctx context.Context, memberID uint) (*models.StudyPlan, error)
	DeletePlan(ctx context.Context, planID string) error
	DeletePlans(ctx context.Context, planIDs []int64) error
	CheckStudyTask(ctx context.Context, memberID uint, productID uint) error

	AcceptPlan(ctx context.Context, planID int64) error
	CompletePlan(ctx context.Context, planID int64) error
	RejectPlan(ctx context.Context, payload *models.StudyPlanRejectPayload) error
	RetryPlan(ctx context.Context, memberID uint) error

	HandleUncompletedPlans(ctx context.Context) error

	CreateTask(ctx context.Context, task *models.StudyTask) error
	UpdateTask(ctx context.Context, task *models.StudyTask) error
	DeleteTask(ctx context.Context, taskID int64) error
}

type studyPlanService struct {
	db *gorm.DB
}

func NewStudyPlanService(db *gorm.DB) StudyPlanService {
	return &studyPlanService{db: db}
}

func (s *studyPlanService) CreatePlan(ctx context.Context, memberID uint) (*models.StudyPlan, error) {
	repo := repository.NewStudyPlanRepo(s.db)

	return repo.Create(ctx, memberID)
}

func (s *studyPlanService) GetPlan(ctx context.Context, memberID uint) (models.StudyPlan, error) {
	repo := repository.NewStudyPlanRepo(s.db)

	return repo.GetByMemberID(ctx, memberID)
}

func (s *studyPlanService) GetPlans(ctx context.Context, page *models.PageOption, filter *models.StudyPlanFilter) ([]models.StudyPlan, error) {
	repo := repository.NewStudyPlanRepo(s.db)

	return repo.Fetch(ctx, page, filter)
}

func (s *studyPlanService) CheckStudyTask(ctx context.Context, memberID uint, productID uint) error {
	repo := repository.NewStudyPlanRepo(s.db)

	plan, err := repo.GetByMemberID(ctx, memberID)

	if err != nil {
		if err.Error() == "record not found" {
			return nil
		}

		return err
	}

	if plan.Status != models.PlanStatusActive {
		return nil
	}

	for i, task := range plan.Tasks {
		if task.ProductID != productID || utils.IsDateBefore(task.OrderDeadline, time.Now()) ||
			task.Status != models.StudyTaskStatusPending {
			continue
		}

		plan.Tasks[i].Status = models.StudyTaskStatusCompleted
		plan.Tasks[i].CompletedAt = null.TimeFrom(time.Now())

		if err := repo.UpdateTask(ctx, &plan.Tasks[i]); err != nil {
			return err
		}
	}

	isPlanCompleted := true
	for _, task := range plan.Tasks {
		if task.Status != models.StudyTaskStatusCompleted {
			isPlanCompleted = false
			break
		}
	}

	if isPlanCompleted {
		if err := s.CompletePlan(ctx, plan.ID); err != nil {
			return err
		}
	}

	return nil
}

func (s *studyPlanService) AcceptPlan(ctx context.Context, planID int64) error {
	repo := repository.NewStudyPlanRepo(s.db)

	plan, err := repo.GetByID(ctx, planID)
	if err != nil {
		return err
	}

	if plan.Status != models.PlanStatusPending {
		return errors.New("無法接受此計畫")
	}

	return repo.AcceptPlan(ctx, planID)
}

func (s *studyPlanService) CompletePlan(ctx context.Context, planID int64) error {
	tx := s.db.WithContext(ctx)

	taskService := NewChallengeService(tx)
	repo := repository.NewStudyPlanRepo(tx)

	plan, err := repo.GetByID(ctx, planID)
	if err != nil {
		return err
	}

	if plan.Status != models.PlanStatusActive {
		return errors.New("無法完成此計畫")
	}

	if err := repo.CompletePlan(ctx, planID); err != nil {
		return err
	}

	if err := taskService.CheckStudyPlanTask(ctx, &models.ChallengeStudyPlanTaskPayload{
		MemberID: plan.MemberID,
	}); err != nil {
		return err
	}

	return nil
}

func (s *studyPlanService) RejectPlan(ctx context.Context, payload *models.StudyPlanRejectPayload) error {
	repo := repository.NewStudyPlanRepo(s.db)

	plan, err := repo.GetByID(ctx, payload.PlanID)
	if err != nil {
		return err
	}

	if plan.Status != models.PlanStatusPending && plan.Status != models.PlanStatusActive {
		return errors.New("錯誤的狀態參數")
	}

	if payload.Reason == "" {
		return errors.New("請填寫拒絕原因")
	}

	return repo.RejectPlan(ctx, &models.StudyPlanRejectPayload{
		PlanID: plan.ID,
		Status: payload.Status,
		Reason: payload.Reason,
	})
}

func (s *studyPlanService) RetryPlan(ctx context.Context, memberID uint) error {
	repo := repository.NewStudyPlanRepo(s.db)

	plan, err := repo.GetByMemberID(ctx, memberID)
	if err != nil {
		return err
	}

	if plan.Status != models.PlanStatusFailed {
		return errors.New("錯誤的參數，無法重新挑戰")
	}

	// 僅可以重新挑戰一個月內的任務
	d := plan.CreatedAt
	expiredAt := time.Date(d.Year(), d.Month(), d.Day(), 23, 59, 59, 0, time.Local)
	if expiredAt.Before(time.Now()) {
		return errors.New("以超過可重新挑戰的時間，無法重新挑戰")
	}

	// 重新挑戰時，將原本的計畫標記為進行中
	err = repo.RetryPlan(ctx, &plan)
	if err != nil {
		return err
	}

	// 取得原本的計畫中，失敗的任務
	tasks, err := repo.FetchTasks(ctx, &models.StudyTaskFilter{
		PlanID: plan.ID,
		Status: []models.StudyTaskStatus{models.StudyTaskStatusFailed},
	})
	if err != nil {
		return err
	}

	// 重新設定任務的狀態，及挑戰期限
	for _, task := range tasks {
		task.Status = models.StudyTaskStatusPending
		task.OrderDeadline = time.Now().AddDate(0, 0, task.DeadlineDays)

		if err := repo.UpdateTask(ctx, &task); err != nil {
			return err
		}
	}

	return nil
}

func (s *studyPlanService) HandleUncompletedPlans(ctx context.Context) error {
	repo := repository.NewStudyPlanRepo(s.db)
	plans, err := repo.FetchOverduePlans(ctx)
	if err != nil {
		return err
	}

	for _, plan := range plans {
		if len(plan.Tasks) == 0 {
			continue
		}

		tx := s.db.Begin()
		repo := repository.NewStudyPlanRepo(tx)

		// if err := repo.RejectPlan(ctx, &models.StudyPlanRejectPayload{
		// 	PlanID: plan.ID,
		// 	Status: models.PlanStatusFailed,
		// 	Reason: "未在指定時間內完成課表任務",
		// }); err != nil {
		// 	tx.Rollback()
		// 	continue
		// }

		for _, task := range plan.Tasks {
			if task.Status != models.StudyTaskStatusPending {
				continue
			}

			task.Status = models.StudyTaskStatusFailed
			task.FailedAt = null.TimeFrom(time.Now())

			if err := repo.UpdateTask(ctx, &task); err != nil {
				tx.Rollback()
				continue
			}
		}

		tx.Commit()
	}

	return nil
}

func (s *studyPlanService) CreateTask(ctx context.Context, task *models.StudyTask) error {
	repo := repository.NewStudyPlanRepo(s.db)

	diff := math.Abs(time.Since(task.OrderDeadline).Hours() / 24)

	task.DeadlineDays = int(math.Ceil(diff))

	// 建立 Task
	if err := repo.CreateTask(ctx, task); err != nil {
		return err
	}

	// 檢查會員是否已購買該課程，如果有則自動設為完成
	if err := s.checkAndCompleteTaskIfPurchased(ctx, task); err != nil {
		return err
	}

	return nil
}

// checkAndCompleteTaskIfPurchased 檢查會員是否已購買課程，如果有則將 Task 設為完成
func (s *studyPlanService) checkAndCompleteTaskIfPurchased(ctx context.Context, task *models.StudyTask) error {
	repo := repository.NewStudyPlanRepo(s.db)

	// 透過 PlanID 獲取 StudyPlan 以取得 MemberID
	plan, err := repo.GetByID(ctx, task.PlanID)
	if err != nil {
		return err
	}

	// 檢查會員是否已購買該課程
	// 條件：啟用(Y)、暫停中(S)、或停用但未過期(N + expired_at > NOW())
	var memberProduct models.MemberProduct
	err = s.db.WithContext(ctx).
		Where("member_id = ? AND product_id = ?", plan.MemberID, task.ProductID).
		Where("status = 'Y' OR status = 'S' OR (status = 'N' AND expired_at > NOW())").
		First(&memberProduct).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 會員未購買該課程，保持 Task 為 pending 狀態
			return nil
		}
		return err
	}

	// 會員已購買該課程，將 Task 設為完成
	task.Status = models.StudyTaskStatusCompleted
	task.CompletedAt = null.TimeFrom(time.Now())

	return repo.UpdateTask(ctx, task)
}

func (s *studyPlanService) UpdateTask(ctx context.Context, task *models.StudyTask) error {
	repo := repository.NewStudyPlanRepo(s.db)

	existingTask, err := repo.GetTaskByID(ctx, task.ID)
	if err != nil {
		return err
	}

	if existingTask.Status != task.Status {
		if task.Status == models.StudyTaskStatusCompleted {
			task.CompletedAt = null.TimeFrom(time.Now())
		} else if task.Status == models.StudyTaskStatusFailed {
			task.FailedAt = null.TimeFrom(time.Now())
		}
	}

	return repo.UpdateTask(ctx, task)
}

func (s *studyPlanService) DeleteTask(ctx context.Context, taskID int64) error {
	repo := repository.NewStudyPlanRepo(s.db)

	return repo.DeleteTask(ctx, taskID)
}

func (s *studyPlanService) GetStudyPlanOverview(ctx context.Context, page *models.PageOption, filter *models.StudyPlanFilter) (*models.StudyPlanOverview, error) {
	repo := repository.NewStudyPlanRepo(s.db)

	plans, err := repo.FetchOverview(ctx, page, filter)
	if err != nil {
		return nil, err
	}

	overview := &models.StudyPlanOverview{
		Plans: plans,
		Page:  *page,
	}

	return overview, nil
}

func (s *studyPlanService) DeletePlan(ctx context.Context, planID string) error {
	repo := repository.NewStudyPlanRepo(s.db)

	// 轉換planID為int64
	id, err := strconv.ParseInt(planID, 10, 64)
	if err != nil {
		return fmt.Errorf("無效的課表ID")
	}

	return repo.DeletePlan(ctx, id)
}

func (s *studyPlanService) DeletePlans(ctx context.Context, planIDs []int64) error {
	repo := repository.NewStudyPlanRepo(s.db)

	return repo.DeletePlans(ctx, planIDs)
}
